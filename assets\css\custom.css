body {
	font-family: 'sansationregular', sans-serif;
	color:#ffffff;
	background: #053251;
	font-size:16px;
	font-weight:500;
}


a {
    color: #f9db31;
    text-decoration: none;
}


.offset-md-1 {
    margin-left: 12.**********%;
}

.account-buttons .btn {display:block;}

.prices {

}

.price {
    display: inline-block;
    padding: 8px 7px 0px;
    font-size: 14px;
}

.value p {
    font-size: 14px;
    color: #fec720;
    font-weight: 600;
    margin-bottom: 0px;
    margin-top: 3px;
}

.price figure {
    float: left;
    margin-right: 5px;
    position: relative;
    margin-bottom: 0px;
}

.value {
    float: left;
    text-align: left;
    line-height:18px;
}

.value span {font-weight:600; font-size:12px;}
.navbar-brand {
    margin-top: 36px;
    display: block;
}

.greenup {

	color:#13BE88;

}

.purpledown {

	color:#EF4A2F;

}


.greenup1 {
	width:15px;
	height:15px;
	background: url(../../assets/img/green-up.png) no-repeat left top;
    position: absolute;
    left: 20px;
    top: 18px;

}

.purpledown1 {
	width:15px;
	height:15px;
	background: url(../../assets/img/purple-down.png) no-repeat left top;
    position: absolute;
    left: 20px;
    top: 18px;

}

.top-part {
    background: #02152D url(../../assets/img/banner-bg.jpg) no-repeat left top;
    background-size: cover;
    position: relative;
    overflow: hidden;
    padding-bottom: 280px;
}

.top-wrapper-sub {
	padding-bottom: 30px;
}

.top-part-inner {
		background: url(../../assets/img/menu-bg2.png) no-repeat right top;
		position:relative;
}

.yellow-box {
    position: absolute;
    left: 0px;
    top: 50%;
    background: url(../../assets/img/orange-line.png) no-repeat left center;
    width: 47px;
    height: 661px;
}

.telegram-top {
    position: fixed;
    left: 40px;
    top: 20%;
    background: #0D2549 url(../../assets/img/telegram-top.png) no-repeat center;
    width: 67px;
    height: 251px;
    border: 2px solid #039BE5;
}


.menu-area {
    background: url(../../assets/img/menu-bg.png) no-repeat center -15px;
    text-align: center;
}

.bannerright p {
    font-size: 30px;
    color: #ffffff;
    margin-bottom: 20px;
}


.bottom-info {margin-top:10px;}

.bottom-info p {
    font-size: 14px;
    font-weight: 700;
    text-transform: uppercase;
    color: #3899da;
    margin-left: 15px;
    margin-bottom:0px;
}

.bottom-info figure {margin-bottom:0px;}

.bottom-info p span {color:white; display:block;}


.profit-box p {
    font-size: 14px;
    color: #ff96ea;
    margin-bottom: 0px;
}




.bannerright h3 {
color: #b868f0;
    font-size: 16px;
    font-weight: 700;
    text-transform: uppercase;
    margin-bottom: 0px;

}

.profit-box h3 {
color: white;
    font-size: 24px;
    font-weight: 700;
    text-transform: uppercase;
    margin-bottom: 0px;

}

.bannerright h3 span {
	color:#3c9cdc;
}




.navbar-expand-lg {padding:0px;}



.navbar-expand-lg .navbar-nav .nav-link {
color: #ffffff;
    font-size: 16px;
    font-weight: 640;
    padding: 0.7rem 0.2rem 0.8rem;
    border-radius: 0px;
    margin: 0px 8px;
    text-transform: uppercase;
    position: relative;

  
  }

.navbar-expand-lg .navbar-nav .nav-link:hover {color:#FEC720;}


.navbar-toggler i {color:white;}

.navbar .btn {
	font-size: 18px;
    padding: 0.2rem 1.2rem;
    border-radius: 4px;
    margin: 0px 3px;
    color: #FF9A07;

}


.btn-check:active+.btn-secondary, .btn-check:checked+.btn-secondary, .btn-secondary.active, .btn-secondary:active, .show>.btn-secondary.dropdown-toggle {
    color: #fff;
background-color: #18BB63;
    border-color: #18BB63;
}


.btn-warning {
	/* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#f4970c+0,feb620+100 */
	background: rgb(244,151,12); /* Old browsers */
	background: -moz-linear-gradient(left, rgba(244,151,12,1) 0%, rgba(254,182,32,1) 100%); /* FF3.6-15 */
	background: -webkit-linear-gradient(left, rgba(244,151,12,1) 0%,rgba(254,182,32,1) 100%); /* Chrome10-25,Safari5.1-6 */
	background: linear-gradient(to right, rgba(244,151,12,1) 0%,rgba(254,182,32,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f4970c', endColorstr='#feb620',GradientType=1 ); /* IE6-9 */
    color: #ffffff;
    font-size: 18px;
    font-weight: 400;
    border: 0px;
    padding: 0.8rem 1.2rem;
    border-radius:10px;
    text-transform:uppercase;
}


.btn-warning:hover {
	/* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#f4970c+0,fecc20+100 */
	background: rgb(244,151,12); /* Old browsers */
	background: -moz-linear-gradient(left, rgba(244,151,12,1) 0%, rgba(254,204,32,1) 100%); /* FF3.6-15 */
	background: -webkit-linear-gradient(left, rgba(244,151,12,1) 0%,rgba(254,204,32,1) 100%); /* Chrome10-25,Safari5.1-6 */
	background: linear-gradient(to right, rgba(244,151,12,1) 0%,rgba(254,204,32,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f4970c', endColorstr='#fecc20',GradientType=1 ); /* IE6-9 */
	color:#ffffff;
}


.btn-primary, .sbmt {
	/* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#0976bf+0,069be1+100 */
	background: rgb(9,118,191); /* Old browsers */
	background: -moz-linear-gradient(left, rgba(9,118,191,1) 0%, rgba(6,155,225,1) 100%); /* FF3.6-15 */
	background: -webkit-linear-gradient(left, rgba(9,118,191,1) 0%,rgba(6,155,225,1) 100%); /* Chrome10-25,Safari5.1-6 */
	background: linear-gradient(to right, rgba(9,118,191,1) 0%,rgba(6,155,225,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#0976bf', endColorstr='#069be1',GradientType=1 ); /* IE6-9 */
    color: #ffffff;
    font-size: 18px;
    font-weight: 400;
    border: 0px;
    padding: 0.8rem 1.2rem;
    border-radius:10px;
    text-transform:uppercase;
}

.btn-primary:hover, .btn-primary:focus, .sbmt:focus {
	/* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#0976bf+0,1eadf0+100 */
	background: rgb(9,118,191); /* Old browsers */
	background: -moz-linear-gradient(left, rgba(9,118,191,1) 0%, rgba(30,173,240,1) 100%); /* FF3.6-15 */
	background: -webkit-linear-gradient(left, rgba(9,118,191,1) 0%,rgba(30,173,240,1) 100%); /* Chrome10-25,Safari5.1-6 */
	background: linear-gradient(to right, rgba(9,118,191,1) 0%,rgba(30,173,240,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#0976bf', endColorstr='#1eadf0',GradientType=1 ); /* IE6-9 */
}







.btn-info {
	/* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#3697d8+0,1c7ac8+100 */
	background: rgb(54,151,216); /* Old browsers */
	background: -moz-linear-gradient(top, rgba(54,151,216,1) 0%, rgba(28,122,200,1) 100%); /* FF3.6-15 */
	background: -webkit-linear-gradient(top, rgba(54,151,216,1) 0%,rgba(28,122,200,1) 100%); /* Chrome10-25,Safari5.1-6 */
	background: linear-gradient(to bottom, rgba(54,151,216,1) 0%,rgba(28,122,200,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#3697d8', endColorstr='#1c7ac8',GradientType=0 ); /* IE6-9 */
    color: #ffffff;
    font-size: 12px;
    font-weight: 600;
    border: 0px;
        text-transform: uppercase;
}

.btn-info:hover, .btn-info:focus {
/* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#4ab2f6+0,2e96e7+100 */
background: rgb(74,178,246); /* Old browsers */
background: -moz-linear-gradient(top, rgba(74,178,246,1) 0%, rgba(46,150,231,1) 100%); /* FF3.6-15 */
background: -webkit-linear-gradient(top, rgba(74,178,246,1) 0%,rgba(46,150,231,1) 100%); /* Chrome10-25,Safari5.1-6 */
background: linear-gradient(to bottom, rgba(74,178,246,1) 0%,rgba(46,150,231,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#4ab2f6', endColorstr='#2e96e7',GradientType=0 ); /* IE6-9 */
color:white;
}

input::placeholder {
    color:#B0ACB2!important;
}

.banners-bottom {padding-bottom:30px;}

.days-online {
    background: url(../../assets/img/shape-big.png) no-repeat center;
    text-align: center;
    display: inline-block;
    padding: 15px 30px 15px;
    font-size: 20px;
    min-width: 330px;
    margin-left: 30px;
    line-height: 70px;
}

.days-online b {color:#ffb922;}



#banner-img {
    position: relative;
    margin: 0 auto;
    margin-top: -100px;
}


#banner-img img {
  position:absolute;
  left:0;
  -webkit-transition: opacity 1s ease-in-out;
  -moz-transition: opacity 1s ease-in-out;
  -o-transition: opacity 1s ease-in-out;
  transition: opacity 1s ease-in-out;
}

#banner-img img.top:hover {
  opacity:0;
}



  @keyframes cf3FadeInOut {
  0% {
  opacity:1;
}
15% {
opacity:0.8;
}
35% {
opacity:0.7;
}
55% {
opacity:0.5;
}
75% {
opacity:0.3;
}

100% {
opacity:0;
}
}

#banner-img img.top {
animation-name: cf3FadeInOut;
animation-timing-function: ease-in-out;
animation-iteration-count: infinite;
animation-duration: 2s;
animation-direction: alternate;
}


.banner-img figure {margin-top:-100px;}

.stat-block {
    background: url(../../assets/img/stat1.png) no-repeat center top;
    text-align: center;
    padding-top: 50px;
    margin-top: 50px;
    text-transform: uppercase;
    min-height: 248px;
}

.stat-block2 {
	background: url(../../assets/img/stat2.png) no-repeat center top;
    text-align: center;
    margin-top:20px;
}

.stat-block figure {margin-bottom:0px;}

.stat-block p {
	font-size:14px;
	margin-bottom:5px;
	
}

.stat-block h2 {
	font-size:24px;
	 font-family: 'sansationbold', sans-serif;
	 font-weight:400;
	
}

.stat-block h2 span {
	font-size:18px;
	display:block;
}

.banner-bottom {
    background: url(../../assets/img/welcome-bg.png) no-repeat center top;
    margin-top: -276px;
    position: relative;
    min-height: 250px;
    margin-bottom: 40px;
}

.banner-bottom-sub {
    min-height: 246px;
}

.banner-middle {
	padding:50px;
	text-align:center;
}

.banner-middle h2 {
	text-transform:uppercase;
	font-size:36px;
}

.banner-middle h2 span {
	color:#ffc23e;
	 font-family: 'sansationbold', sans-serif;
	 font-weight:400;
}

.banner-middle p {
    font-size: 20px;
    padding: 5px 70px;
    margin-bottom: 0px;
}

.forgot-password a {
    color: #d1afe8;
    font-weight: 600;

}


.features-top h2 {
	font-size:24px;
}


.top-logo-container p {
    text-align: center;
    text-transform: uppercase;
    font-size: 16px;
    margin-top: 0px;

}


.about_block figure {
	margin-top:30px;
}

.about_block figure img {max-width:100%;}

.security h1 {
    color: white;
    font-size: 30px;
    text-align: right;
    margin-top: 10px;
}

.security figure {margin-top:0px; padding-left:10px;}


.bannerright {

    margin-top: 20px;
    z-index: 10000;
    position: relative;
}


.bannerinide {
	width: 100%;
	float: left;
	position: relative;
	z-index: 9;
}
.btn.openacc {
    border-radius: 36px;
    padding: 15px 35px;
    margin: 10px 0px;
    display: inline-block;
    color: white;
    font-size:16px;
    letter-spacing:0;
    font-weight:700;
    text-transform:uppercase;
}


.button-group {
    padding: 18px 0px;


}

.button-group .btn {
	font-size:12px;
	font-weight:600;
	color:white;
	border-radius:36px;
	text-transform:uppercase;
	    padding: 12px 15px;
}

.button-group .btn-secondary {
	/* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#ba60fb+0,e881ff+100 */
	background: rgb(186,96,251); /* Old browsers */
	background: -moz-linear-gradient(45deg, rgba(186,96,251,1) 0%, rgba(232,129,255,1) 100%); /* FF3.6-15 */
	background: -webkit-linear-gradient(45deg, rgba(186,96,251,1) 0%,rgba(232,129,255,1) 100%); /* Chrome10-25,Safari5.1-6 */
	background: linear-gradient(45deg, rgba(186,96,251,1) 0%,rgba(232,129,255,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ba60fb', endColorstr='#e881ff',GradientType=1 ); /* IE6-9 fallback on horizontal gradient */
}



.button-group .btn-outline-secondary {border:2px solid white;     padding: 10px 15px;}

.button-group .btn-outline-secondary:hover {
	background:#1858BE;
}

@media (min-width: 1400px) {
.container, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl {
    max-width: 1170px;
}
}


.security-buttons {
	background: rgb(255,152,5);
    background: -moz-linear-gradient(45deg, rgba(255,152,5,1) 0%, rgba(255,194,42,1) 100%);
    background: -webkit-linear-gradient(45deg, rgba(255,152,5,1) 0%,rgba(255,194,42,1) 100%);
    background: linear-gradient(45deg, rgba(255,152,5,1) 0%,rgba(255,194,42,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ff9805', endColorstr='#ffc22a',GradientType=1 );
    color:white;
    border-radius:6px;
    margin-bottom:10px;
}

.security-buttons figure {
	margin:15px;
}



.security-buttons p {
	text-transform: none;
    font-weight: 500;
    font-size: 24px;
    padding: 15px;
    border-radius: 0px 4px 4px 0px;
    line-height: 30px;
    padding-top: 20px;

}

.bannerwrap {
	padding: 50px 0 0px;


}


.banner-1-shape {
    position: absolute;
    width: 100%;
    bottom: 0;
    left: 0;
    z-index: 0;
}

.bannerwrap h1 {
    font-weight: 400;
    color: #ffffff;
    font-size: 48px;
    margin-bottom: 20px;
    line-height: 60px;
    text-transform:uppercase;
}



.bannerwrap h1 span {
    color: #ffb922;
    font-weight:400;
    display:block;
    font-size:60px;
    font-family: 'sansationbold';
}

.bannercontent {
    position: relative;
    width: 100%;
    padding-bottom: 170px;

}

.plans-title {
    font-size: 48px;
    text-align: left;
    color: #1c43aa;
    padding: 20px 0px;

}



.plans-title span {
    color: #333f60;
    font-size: 30px;
    border-left: 1px solid #244BAD;
    display: inline-block;
    margin-left: 20px;
    padding-left: 20px;

}

.planswrap {
    background: #ffffff url(../../assets/img/middle-bg.png) repeat-x center top;
    padding-bottom: 20px;
    padding-top: 20px;
}

.plans-content {
	padding:30px;
}



.stat-bg {
    color: #ffffff;
    text-align: left;
    font-size:14px;

}


.stat-bg2 {
	border:2px solid #32A1BD;
}


.stat-bg p {
    font-size: 14px;
    padding-top: 0px;
    font-weight: 600;
    margin-bottom: 0px;
    line-height: 20px;
    padding-left: 10px;
}



.stat-bg .comp2 {
    border-left: 1px solid #8835C5;
    margin-left: 10px;

}

.stat-bg p span {
	color:#4795ed;
	display:block;
}



.stat-bg  figure {
    padding: 0px 2px;
    margin-bottom: 0px;
    border-radius: 0px 36px 36px 0px;
    margin-right: 0px;
}


.stat-bg2  figure {
    background: rgb(52,164,192);
    background: -moz-linear-gradient(top, rgba(52,164,192,1) 0%, rgba(21,126,152,1) 46%, rgba(79,189,217,1) 100%);
    background: -webkit-linear-gradient(top, rgba(52,164,192,1) 0%,rgba(21,126,152,1) 46%,rgba(79,189,217,1) 100%);
    background: linear-gradient(to bottom, rgba(52,164,192,1) 0%,rgba(21,126,152,1) 46%,rgba(79,189,217,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#34a4c0', endColorstr='#4fbdd9',GradientType=0 );
    padding: 8px 20px 8px 13px;
    margin-bottom: 0px;
    border-radius: 0px 36px 36px 0px;
    margin-right: -3px;
}

.insurance {line-height:32px;}

.insurance a {
	color:white;
	text-transform:uppercase;
	font-size:12px;
	font-weight:600;
}

.form-control, .form-select {
    display: block;
    width: 100%;
    padding: 0.275rem 0.55rem;
    font-size: 0.7rem;
    font-weight: 400;
    line-height: 1.5;
    color: white;
    background-color: #091e4b;
    background-clip: padding-box;
    border: 1px solid #183E8A;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: 0.25rem;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}


.affiliate-text .btn {
border-radius: 36px;
    font-size: 18px;
    font-weight: 700;
    padding: 15px 35px;
    text-transform: uppercase;
}


.amount {padding: 30px 0px 0px;}


.amount label {
    color: white;
    font-size: 18px;
    font-weight: 600;
    text-transform: uppercase;
    margin-bottom: 0px;
    margin-top: 30px;
}

.profit {padding-top:10px; padding-left:3px;}

.amount .form-control {
    font-size: 48px;
    padding: 0rem;
    line-height: 0.3rem;
    border: 0px;
    max-width: 180px;
    color: #ffc741;
    background: transparent;
    text-align: right;
}

.amount .input-group-text {
    display: flex;
    align-items: center;
    padding: .175rem .55rem;
    font-size: 0.6rem;
    font-weight: 400;
    line-height: 1;
    color: white;
    text-align: center;
    white-space: nowrap;
    background-color: #224493;
    border: 0px;
    border-radius: .25rem;
}


.profit p span {
	color:white;
	font-size:16px;
	font-weight:600;
	display:block;
}



.benefits h1 {
	font-size:33px;
	font-weight:300;
}

.benefits h1 span {font-weight:700;}

.benefits p {font-size:18px; color:#329FF3; border-left:1px solid #329FF3; padding-left:20px;}

.benefits p span {font-size:14px; color:white;}


.about_block {
	background: url(../../assets/img/about-bg.png) no-repeat right top;
    padding-bottom: 120px;
    padding-top:0px;
}


.about-bg h2 {
    font-size: 42px;
    color: #0393d4;
    font-weight: 300;
    text-transform: uppercase;
    line-height: 70px;
	
	
	}

.about-top p {font-size:24px; font-weight:300;}

.about-bg  h2 span {
	font-weight:700;

}

.about-top {text-align:center;}

.text-block {
	padding:30px 0px 10px;
}

.text-block h4 {
    font-size: 24px;
    font-weight: 300;
    margin-bottom: 20px;
    line-height: 36px;
}

.text-block p {
    font-size: 16px;
    font-weight: 500;
    line-height: 30px;
    text-align: justify;
}

.features_block {
    padding: 40px 0px 40px;
    background: #195CC4 url(../../assets/img/data-bg-img.png) no-repeat center;
    background-size: cover;
    color: white;
}


.company-info {
    background: #ffffff url(../../assets/img/stats-bg.jpg) no-repeat center;
    background-size: cover;
    padding: 20px 0px 60px;
}

.company-block img {max-width:100%;}

.payment-block {text-align:center;}

.why-box {
    padding: 50px 0px 0px;
    background: #071635 url(../../assets/img/about-bg.jpg) no-repeat center top;
    background-size: cover;
}

.investment {
    background: url(../../assets/img/calc-bg.png) no-repeat center top;
    margin-top: 40px;
    padding-top: 50px;
    text-align: center;
}

.why-box figure {
}

.why-box figure img {max-width:100%;}

.why-box-text {text-align:left; color:white;}


.why-box-text h2 {
	font-weight:400;
	font-size:30px;
	text-transform: uppercase;
}

.why-box-text h2 span {
	color: #ffc23e;
    font-family: 'sansationbold', sans-serif;
    font-weight: 400;
}

.why-box-text h3 {
	font-weight:400;
	font-size:18px;
	text-transform: uppercase;
	color:#01a3ee;
}


.why-box-text  p {
	text-align:justify;
	font-size:16px;
}
.goal {
    background: url(../../assets/img/goal.png) no-repeat left top;
    padding-left: 83px;
}

.goal h4, .satisfaction h4 {
	color:#ffc23e;
	font-size:24px;
}

.satisfaction {
	 background: url(../../assets/img/satisfaction.png) no-repeat left top;
	 padding-left:83px;
}

.investment p {
    font-size: 24px;

}

.investment p span {
	color:#ffb922;
}

.investment h2 b {
	color:#ffb922;
	    font-family: 'sansationbold', sans-serif;
    font-weight: 400;
}

.calculator-block {
    background: url(../../assets/img/calc-icon.png) no-repeat left bottom;
    padding-bottom: 50px;
}

.return {
    background: url(../../assets/img/shape-sm.png) no-repeat center;
    text-align: center;
    display: inline-block;
    padding-left: 55px;
    font-size: 14px;
    min-width: 300px;
    margin-left: 0px;
    line-height: 22px;
    padding-top: 20px;
}

.return p {
	text-align:left;
	margin-left:5px;
	font-size: 14px;
    text-transform: uppercase;}

.return p span {
	display:block;
	color:#20f078;
	font-size:20px;
}

.plan-box {
	background: url(../../assets/img/plan-orange.png) no-repeat center top;
    padding-top: 50px;
    min-height: 300px;
    background-size: 100%;
    padding-left: 20px;
}

.box-blue {
	background: url(../../assets/img/plan-blue.png) no-repeat center top;
	    background-size: 100%;
}

.plan-box h3 {
    font-size: 72px;
    font-family: 'sansationbold', sans-serif;
    margin-bottom: 0px;
    line-height: 60px;
    letter-spacing:-2px;
	
}


.plan-box h3 span {
	font-size: 48px;
    display: inline-block;
}

.plan-box p {
    font-family: 'sansationbold', sans-serif;
    font-size: 18px;
    margin-bottom: 5px;
    line-height: 20px;
}

.plan-box a {
    background: #091F50;
    border-radius: 36px;
    color: white;
    padding: 5px 15px;
    display: inline-block;
    font-size: 11px;
    text-transform: uppercase;
    font-family: 'sansationbold', sans-serif;
    margin-top: 3px;
}

.investment h2 {
	text-transform: uppercase;
	font-size:36px;
}

.investment h2 span {
	 text-transform: uppercase;
	 color:#ffb922;
	 display:block;
	 font-size:48px;
	     font-family: 'sansationbold', sans-serif;
}





.features-top {
	background:white;
	box-shadow: 0 1.2rem 2rem rgb(10 31 68 / 10%);
	border-radius:10px;
}

.features-top h2 {
    font-size: 24px;
    font-weight: 400;
    padding-right: 0px;
    line-height: 40px;
    margin-top: 10px;
    text-transform: none;
}

.features-top h2 b { font-weight: 700;}

.features {
    background: white;
    border-radius: 10px;
    box-shadow: 0 1.2rem 2rem rgb(10 31 68 / 10%);
    padding-bottom: 25px;
    margin-top: -140px;
    margin-bottom: 30px;

}

.feat-block {
    text-align: center;
    color: #ffffff;
    margin: 15px 0px;
       background: url(../../assets/img/feature-bg.png) no-repeat center;
    min-height: 310px;
    padding-top: 80px;
    line-height: 36px;
}
.feat-block-middle {text-align:center;}

.feat-block-middle .btn-info {
    font-size: 16px;
    font-weight: 700;
    text-transform: uppercase;
    padding: 12px 30px;
    border-radius: 36px;
    margin-top: -150px;
}

.feat-block h3 {
	font-weight:600;
	font-size:20px;
}

.feat-block p {font-size:12px; line-height:20px;}


.feat-top figure {margin-bottom:10px;}

.feat-top h2 {
    font-size: 18px;
    text-transform: uppercase;
    color: white;
    font-weight: normal;
    margin-top: 10px;
    margin-bottom: 0px;
    line-height: 30px;
}



.feat-top h2 b {
	color:#5e5dec;
	font-weight:700;
}

.feature-left h2 {
    font-size: 24px;
    margin-left: 20px;

}


.feature-left {
    padding: 20px 20px;
    margin-top: 20px;
}

.feature-left h2 span {
	font-size:36px;
	font-weight:600;
	color:#0787ea;
	display:block;
}

.buttons {margin-top:10px; text-align:right;}

.company-block {
    background: #ffffff url(../../assets/img/about-bg2.png) no-repeat left top;
    border-radius: 16px;
    padding: 30px 30px 20px;
    filter: drop-shadow(-4px 10px 10px rgba(6, 19, 63, 0.3));
    margin-top: -130px;
}

.company-block p {
    font-size: 14px;
    margin-bottom: 5px;
}

.insurance-text {
	margin-top:60px;
}

.insurance-text ul {padding-left:0px;}

.insurance-text ul li {
    margin-bottom: 15px;
    font-size: 18px;
    background: url(../../assets/img/check.png) no-repeat left 5px;
    padding-left:38px;
    list-style:none;
}

.insurance-text h2 {
	color:#1a79c7;
	font-size:36px;
	font-weight:700;
	margin-bottom:20px;
}

.insurance-text h2  b {color:#e03042;}

.cert-img figure {
	margin-bottom:0px;
}

.cert-img p {
    font-size: 12px;
    text-transform: uppercase;
    margin-top: 5px;
    margin-left: 10px;
    margin-bottom: 0px;
}

.company-text h2 {
color: #851aad;
    text-transform: uppercase;
    font-size: 30px;
    font-weight: 500;
    margin-bottom: 5px;
}

.company-text h3 {
 	color:#1a79c7;
	 text-transform: uppercase;
	 font-size:24px;
	 font-weight:700;
}

.company-text h2 b {font-weight:700;}

.cert1 a {
    background: rgb(233,55,73);
    background: -moz-linear-gradient(top, rgba(233,55,73,1) 0%, rgba(208,36,53,1) 100%);
    background: -webkit-linear-gradient(top, rgba(233,55,73,1) 0%,rgba(208,36,53,1) 100%);
    background: linear-gradient(to bottom, rgba(233,55,73,1) 0%,rgba(208,36,53,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#e93749', endColorstr='#d02435',GradientType=0 );
    color: white;
    width: 130px;
    height: 130px;
    display: block;
    text-align: center;
    border-radius: 100%;
    padding-top: 20px;
    text-transform: uppercase;
    font-weight: 600;
    line-height: 18px;
    filter: drop-shadow(1px 3px 3px rgba(6, 19, 63, 0.3));
    border: 8px solid white;
    font-size: 12px;
    margin: -150px auto 10px;

}

.cert1 a p {font-size:12px;}

.cert2 a {
    background: rgb(54,151,216);
    background: -moz-linear-gradient(top, rgba(54,151,216,1) 0%, rgba(28,122,200,1) 100%);
    background: -webkit-linear-gradient(top, rgba(54,151,216,1) 0%,rgba(28,122,200,1) 100%);
    background: linear-gradient(to bottom, rgba(54,151,216,1) 0%,rgba(28,122,200,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#3697d8', endColorstr='#1c7ac8',GradientType=0 );
    color: white;
    width: 130px;
    height: 130px;
    display: block;
    text-align: center;
    border-radius: 100%;
    padding-top: 20px;
    text-transform: uppercase;
    font-weight: 600;
    line-height: 18px;
    filter: drop-shadow(1px 3px 3px rgba(6, 19, 63, 0.3));
    border: 8px solid white;
    font-size: 12px;
    margin: -150px auto 10px;

}

.cert2 a p {font-size:12px;}

.ssl {
background: #F9FAFA;
    border: 1px solid #FF7563;
    padding: 5px 20px;
    border-radius: 10px;
    margin-top: 10px;
}

.ssl h4 {
    color: #FF7563;
    font-size: 18px;
    font-weight: 700;
    text-transform: uppercase;
    margin-top: 20px;

}

.cert1 img, .cert2 img {max-width:100%;}

.cert1 figure, .cert2 figure {margin-bottom:0px;}

.ssl figure {
	margin-bottom:0px;
	margin-top:-30px;
}

.company-bottom {
	padding-top:10px;
}

.company-bottom  figure {
	margin-bottom:0px;}

.duns {margin-top:20px;}

.duns figure {
	margin-bottom:0px;
	margin-top:5px;
}

.duns p {
    color: #85848d;
    text-transform: uppercase;
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
    margin-left: 10px;
}


.duns p b {
	display:block;
	color:#1a79c7;
	font-size:18px;
}

.about-info {

}



.about-info h2 {
font-size: 20px;
    font-weight: 400;
    margin-top: 20px;
    line-height: 28px;
}

.about-info p {
    text-transform: none;
    color: #ffffff;
    font-size: 14px;
    padding-left: 0px;
    
}

.about-info p  span {
	color:#ffc23e;
}

.about-info p {
    text-transform: none;
    color: #ffffff;
    font-size: 14px;

}

.about-info p.address {
color: #ffffff;
    font-size: 14px;
    display: block;
    text-transform: none;
    line-height: 24px;
    background: url(../../assets/img/location.png) no-repeat left 5px;
    padding-left: 35px;
}
.about-info .btn {
    font-size: 16px;
    padding: 0.6rem 0.6rem;
    display: block;
    margin-bottom:5px;
}
.page-title {
    font-size: 36px;
    text-transform: uppercase;
    color: white;
    font-weight: 400;
    line-height: 50px;
    text-align:center;
}
.page-title span {
	font-size:18px;
	display:block;
}

.page-title b {
color: #ffc23e;
    font-family: 'sansationbold', sans-serif;
}



.feature {
    width: 441px;
    height: 440px;
    background: url(../../assets/img/feature-bg1.png) no-repeat center;
    color: white;
    font-size: 30px;
    text-align: center;
    padding-top: 145px;
    line-height:36px;
    position:absolute;
    top:-30px;
    left:0px;
}

.feature1 {
	width:362px;
	height:378px;
	background: url(../../assets/img/feature-bg2.png) no-repeat center;
	top:213px;
	left:207px;
	padding-top: 110px;
}

.feature2 {
	width:362px;
	height:378px;
	background: url(../../assets/img/feature-bg2.png) no-repeat center;
	top:247px;
	left:0px;
	    padding-top: 110px;
}

.security2 {
	border-radius:6px;
	border:2px solid #56b9d5;
	padding:6px;
	background: -moz-linear-gradient(top, rgba(15,28,60,0.86) 0%, rgba(8,48,102,0.86) 50%, rgba(15,28,60,0.86) 100%);
    background: -webkit-linear-gradient(top, rgba(15,28,60,0.86) 0%,rgba(8,48,102,0.86) 50%,rgba(15,28,60,0.86) 100%);
    background: linear-gradient(to bottom, rgba(15,28,60,0.86) 0%,rgba(8,48,102,0.86) 50%,rgba(15,28,60,0.86) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#db0f1c3c', endColorstr='#db0f1c3c',GradientType=0 );
    margin-top:30px;
}

.security2 h3 {
    color: white;
    font-size: 24px;
    padding-top: 15px;
}

.security2 figure {
	background:white;
	padding:10px;
	border-radius:6px;
	margin-bottom:0px;
	margin-right:20px;
	
	}

.welcomewrap {
	padding: 0 0 50px;
}

.cert {
	padding:30px;
	border-radius:10px;
	background:white;
}

.about-buttons a.btn {
    color: white;
    font-size: 18px;
    text-align: center;
    display: block;
    width: 48%;
}

.about-buttons img {
	display: block;
    margin: 0px auto;
}

.statwrap {
    text-align: center;
    border-radius: 6px;
    color: #48434a;
    font-size: 14px;
    padding: 20px 20px;
    border: 2px solid #e7e6f4;


}

.statwrap p {
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 0px;
    text-transform: uppercase;
    line-height: 30px;
}

.statwrap p span {
    display: block;
    color: white;
    font-weight: 500;
    font-size: 14px;
    margin-top: 10px;
    padding: 0px 20px;

}

.statwrap figure {
	padding: 0px;
	margin-bottom:0px;
}
.statwrap h4 {
	font-size: 36px;
	font-weight: 600;
	margin: 0 0 15px;
}

.commission {
    background: #2d1191;
    background: -moz-linear-gradient(top, #2d1191 0%, #230c7b 100%);
    background: -webkit-linear-gradient(top, #2d1191 0%,#230c7b 100%);
    background: linear-gradient(to bottom, #2d1191 0%,#230c7b 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#2d1191', endColorstr='#230c7b',GradientType=0 );
    padding: 8px 10px;
    border-top: 1px solid #11004F;
    box-shadow: inset 0px 1px 1px #ffffff36;

}

.commission span {
color: #309bfe;
    font-weight: 700;
}

.step {
	display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
        background: #2d1191;
    background: -moz-linear-gradient(top, #2d1191 0%, #230c7b 100%);
    background: -webkit-linear-gradient(top, #2d1191 0%,#230c7b 100%);
    background: linear-gradient(to bottom, #2d1191 0%,#230c7b 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#2d1191', endColorstr='#230c7b',GradientType=0 );
    border: 1px solid #11004F;
    border-radius:4px;
    margin-bottom:10px;

}


.step figure {margin:0px;
	/* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#3417a2+0,271080+100 */
	background: #3417a2; /* Old browsers */
	background: -moz-linear-gradient(top, #3417a2 0%, #271080 100%); /* FF3.6-15 */
	background: -webkit-linear-gradient(top, #3417a2 0%,#271080 100%); /* Chrome10-25,Safari5.1-6 */
	background: linear-gradient(to bottom, #3417a2 0%,#271080 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#3417a2', endColorstr='#271080',GradientType=0 ); /* IE6-9 */
	padding:10px;
	 border-right: 1px solid #11004F;
	 border-radius:4px 0px 0px 4px;
}

.step p {
    font-size: 14px;
    padding: 15px 20px;
    margin-bottom: 0px;
}

.step span {
	font-size: 48px;
    color: #482DAD;
    font-weight: 800;
    padding: 0px 10px;
}

.statistics {
    padding: 0px 0px;
    background: #00133D url(../../assets/img/aff-bg.png) no-repeat center;
    padding-top: 50px;
}

.statistics figure {margin-bottom:0px;     position: relative;}

.statistics h2 {
    font-size: 72px;
    text-transform: uppercase;
    color: #ffffff;
    font-family: 'sansationbold', sans-serif;
    line-height: 60px;
    margin-bottom: 0px;
}

.statistics h2 span {
		font-size:48px;
	text-transform:uppercase;
	color:#ffffff;
	font-weight:800;
}

.statistics h3 {
    font-size: 48px;
    text-transform: uppercase;
    color: #ffffff;
    text-align: center;
    margin: 10px 0px;
    line-height: 48px;
}


.statistics h3 span {
	font-size:48px;
	text-transform:uppercase;
	color:#ffb922;
    font-family: 'sansationbold', sans-serif;
}

.tab-content {
	padding:20px 0px;
}

.stat-middle ul {
	padding:0px;
	margin:0px;

}

.stat-middle ul li {
	list-style:none;
		 display:inline-block;
	
}

.stat-figure {
	 background: url(../../assets/img/stat-figure.png) no-repeat center;
	 text-align:center;
	 width:219px;
	 min-height:231px;
	 padding-top:73px;
	 position: relative;
}


.stat-middle ul li:nth-child(even) .stat-figure {
   background: url(../../assets/img/stat-figure2.png) no-repeat center;
   top: 30px;

}


.stat-middle ul li:nth-child(7) .stat-figure {
   top: 60px;

}


.stat-middle ul li:nth-child(9) .stat-figure {
   top: 80px;

}

.stat-middle ul li:nth-child(4) .stat-figure {
   top: 60px;

}

.stat-middle ul li:nth-child(even) .stat-figure figure {}

.stat-figure  figure {margin-bottom:5px;}

.stat-figure p {
	color:#aeaeae;
	font-size:16px;
}

.stat-figure p b {
	color:#3d3b37;
	font-size:24px;
	font-weight:700;
}

.stat-top {

text-align:center;


}

.stat-top h2 {

    font-size: 16px;
    text-transform: uppercase;
    margin-bottom: 0px;
    font-weight: 400;
}

.stat-top b {
	font-weight:700;
}

.stat-middle {
	color:white;
	padding:10px 10px;
	text-align:center;
}

.stat-middle b {
	display:block;
	font-size:12px;
}


.faq_area {
	 background: url(../../assets/img/faq-bg.png) no-repeat center bottom;
}

.faq_area h2 {
	font-size:36px;
	text-transform:uppercase;
	font-weight:500;
	margin:20px 0px;
}

.faq_area h2 b {font-size:700; color:#1f7dca;}

.faq_area {
	padding:40px 0px;
	font-size:14px;
}

.faq-block p b {
	color:#054e89;
	font-size:24px;
	font-weight:700;
	display:block;
	margin-bottom:10px;
}

.number {
    background: rgb(54,151,216);
    background: -moz-linear-gradient(top, rgba(54,151,216,1) 0%, rgba(28,122,200,1) 100%);
    background: -webkit-linear-gradient(top, rgba(54,151,216,1) 0%,rgba(28,122,200,1) 100%);
    background: linear-gradient(to bottom, rgba(54,151,216,1) 0%,rgba(28,122,200,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#3697d8', endColorstr='#1c7ac8',GradientType=0 );
    font-size: 30px;
    font-weight: 800;
    width: 74px;
    height: 74px;
    margin-right: 20px;
    border-radius: 100%;
    display: block;
    color: white;
    padding: 15px 16px;
    text-align: center;
}

.security {
    background: white;
    padding: 15px 15px;
    filter: drop-shadow(-4px 6px 6px rgba(6, 19, 63, 0.3));
    text-align: center;
    margin-top: 20px;
}

.security img {max-width:100%;}


.security p {margin-bottom:0px;}

.member_info p {
    text-align: left;
    margin-top: 6px;
    margin-bottom: 0px;
    font-size: 18px;
    text-transform: uppercase;
    font-weight: 500;
}

.stat-middle p {
	font-size:18px;
	margin-bottom:0px;
}

.stat-middle p span {
	color:#94cef4;
	font-size:12px;
	display:block;
}

.nav-tabs {
    border-bottom: 0px;
}

.nav-tabs .nav-link {
    color: #ffffff;
    font-size: 13px;
    font-weight: 600;
    padding:0.22rem 0.7rem;
    border-radius: 0px;
    margin: 0px 10px;
    text-transform: uppercase;
    background: rgb(219,221,222);
    background: -moz-linear-gradient(top, rgba(219,221,222,1) 0%, rgba(198,198,204,1) 100%);
    background: -webkit-linear-gradient(top, rgba(219,221,222,1) 0%,rgba(198,198,204,1) 100%);
    background: linear-gradient(to bottom, rgba(219,221,222,1) 0%,rgba(198,198,204,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#dbddde', endColorstr='#c6c6cc',GradientType=0 );
    position: relative;
    border: 0px;
}



.nav-tabs .nav-link:hover {
	box-shadow:-10px 5px 10px #00000057;
	
	}


.nav-tabs .nav-link:after {
    content: '';
    position: absolute;
    right: -10px;
    top: 0px;
    width: 15px;
    height: 54px;
    background: url(../../assets/img/arrow-rightg.png) no-repeat left top;
  }

.nav-tabs .nav-link:before {
    content: '';
    position: absolute;
    left: -10px;
    top: 0px;
    width: 13px;
    height: 54px;
    background: url(../../assets/img/arrow-leftg.png) no-repeat left top;
  }


.nav-tabs .nav-link h4 {
    font-size: 14px;
    text-align: left;
    padding-right: 20px;
    margin-bottom: 0px;
    color:#5f535f;
        margin-top: 2px;


}

.nav-tabs .nav-link h4 b {display:block; font-size:18px;}

.nav-tabs .nav-link p  {
    font-size: 11px;
    text-align: right;
    padding-left: 20px;
    margin-bottom: 0px;
    color:#5f535f;
        margin-top: 2px;
}

.nav-tabs .nav-link p span {
	display:block;
	font-size:18px;
	font-weight:600;
}

.nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active {
    color: #ffffff;
    background: rgb(58,155,219);
    background: -moz-linear-gradient(top, rgba(58,155,219,1) 0%, rgba(24,118,197,1) 100%);
    background: -webkit-linear-gradient(top, rgba(58,155,219,1) 0%,rgba(24,118,197,1) 100%);
    background: linear-gradient(to bottom, rgba(58,155,219,1) 0%,rgba(24,118,197,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#3a9bdb', endColorstr='#1876c5',GradientType=0 );
}

.nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active h4, .nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active p {
	color:#ffffff;
}

.nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active:after {
	  background: url(../../assets/img/arrow-rightb.png) no-repeat left top;
}

.nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active:before {
	  background: url(../../assets/img/arrow-leftb.png) no-repeat left top;
}

.stat-col {
	border-bottom:1px solid #6c98de;
	padding:5px 20px;
}

.border-0 {border-bottom:0px;}

.top {text-align:center;}

.top1 {margin-top:100px;}

.top2 {margin-top:50px;}

.top span {
    display: block;
    font-size: 24px;
    margin-top: 10px;
    color: #315bc9;
    font-weight: 700;

    
}

.top p {
	margin-bottom:0px;
	line-height:24px;
	padding-top:0px;
	font-weight:600;
	color:#2f353a;
}


.affiliate-text {padding-top:30px;}

.affiliate-text h2 {
	color:#ffffff;
	font-size:30px;
	font-weight:400;
	  text-transform: uppercase;
}

.affiliate-text h2 b {
	font-weight:700;
	font-size:48px;
	display:block;
}


.affiliate-text p {
	font-size:30px;
	line-height:48px;
	}
	
.affiliate-text p b{
	color:#315ac8;
}	


.affiliate_img figure {
    margin-top: -50px;
    margin-bottom: 0px;
}

.affiliate_img figure img {
    max-width: 100%;
    margin-top: -80px;
}
.affiliate_area {
    background: #00133D url(../../assets/img/bottom-bg.jpg) no-repeat center top;
    padding: 30px 0px 40px;
    background-size: cover;
}

.affiliate_area h1 {
    font-weight: 400;
    text-transform: uppercase;
    color: #ffffff;
    font-size: 60px;

}


.affiliate_text p {
	font-size:16px;
	line-height:28px;
}

.affiliate_text a {
	    background: #ffe500;
    background: -moz-linear-gradient(top, #ffe500 0%, #ffc908 100%);
    background: -webkit-linear-gradient(top, #ffe500 0%,#ffc908 100%);
    background: linear-gradient(to bottom, #ffe500 0%,#ffc908 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffe500', endColorstr='#ffc908',GradientType=0 );
    color:#132048;
    font-size:16px;
    font-weight:600;
    padding:20px 30px;
    display:inline-block;
    border-radius:6px;
    text-transform:uppercase;
    text-decoration:none;
}

.affiliate_text a:hover {background: #ffe500;}

.affiliate_area h1 span {
	color:#feb300;
}

.affiliate_image {
    position: relative;
    padding: 30px 0px;
	
}

.affiliate_image img {max-width:100%;}

.payment-block figure {

    padding: 0px 5px;
    display: inline-block;
    margin: 0px;
    border: 0px;
    padding-top: 5px;
    text-align: center;
    margin-bottom: 3px;
}

.payment-block img {

}

.aff-percent {
color: #ffffff;
    font-size: 92px;
    font-weight: 300;
    padding: 0.28rem 0rem;
    border-radius: 0px;
    margin: 0px 10px;
    text-transform: uppercase;
    background: rgb(241,11,196);
    background: -moz-linear-gradient(top, rgba(241,11,196,1) 0%, rgba(177,10,142,1) 100%);
    background: -webkit-linear-gradient(top, rgba(241,11,196,1) 0%,rgba(177,10,142,1) 100%);
    background: linear-gradient(to bottom, rgba(241,11,196,1) 0%,rgba(177,10,142,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f10bc4', endColorstr='#b10a8e',GradientType=0 );
    position: relative;
    margin-top: 0px;
    display: block;
    text-align: center;
    margin-bottom: 10px;
    line-height: 100px;
}

.aff-percent:before {
    content: '';
    position: absolute;
    left: -53px;
    top: 0px;
    width: 53px;
    height: 123px;
    background: url(../../assets/img/aff-arrow-right.png) no-repeat left center;
}

.aff-percent:after {
    content: '';
    position: absolute;
    right: -53px;
    top: 0px;
    width: 53px;
    height: 123px;
    background: url(../../assets/img/aff-arrow-left.png) no-repeat right center;
}

.aff-percent span {font-size:60px;}

.percent h4 {
    color: white;
    font-size: 56px;
    text-transform: uppercase;
    font-weight: 700;
    margin-top:20px;
    text-align:center;
}

.percent span {
    font-size: 48px;
    display: inline-block;
    margin-top: 15px;
    color: white;
}

.details p {
    color: white;
    font-size: 12px;
    text-transform: uppercase;
    font-weight: 600;
    line-height: 18px;
}

.details h5 {
	font-weight:700;
	font-size:20px;
	color:white;
	text-transform: uppercase;
}

.percent figure {
    width: 97px;
    height: 100px;
    background: url(../../assets/img/aff-icon-bg.png) no-repeat center;
    display: block;
    font-size: 36px;
    text-align: center;
    line-height: 102px;
    margin: 0px auto;
    font-weight: normal;
    margin-top: -40px;
}


.percent h2 {
    font-size: 60px;
    margin-bottom: 0px;
    line-height: 50px;
}

.percent h2 span {font-size:36px;}

.percent p {
    font-weight: 400;
    color: white;
    font-size: 18px;
    margin-bottom: 0px;
    }

.aff-bottom {
	text-align:center;
	font-size:20px;
	line-height:30px;
}

.nav-pills .nav-link {
    border: 0px;
    border-radius: 6px 6px 0px 0px;
    color: white;
    font-size: 16px;
    margin-right: 10px;
    padding: 1rem 2rem;
    background: #243A78;
    font-weight: 400;
    text-transform: uppercase;
    z-index: 0;
    position: relative;
}

.nav-pills .nav-link.active, .nav-pills .show>.nav-link {
background: rgb(244,151,12);
    background: -moz-linear-gradient(left, rgba(244,151,12,1) 0%, rgba(254,182,32,1) 100%);
    background: -webkit-linear-gradient(left, rgba(244,151,12,1) 0%,rgba(254,182,32,1) 100%);
    background: linear-gradient(to right, rgba(244,151,12,1) 0%,rgba(254,182,32,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f4970c', endColorstr='#feb620',GradientType=1 );
	color:#ffffff;
	 z-index:10000;
}


.pay-button {
    display: inline-block;
    margin: 3px 1px;
}

.pay-button img {width:30px;}

.pay-button .btn {font-size:14px;}

.nav-pills .nav-link img {margin-left:10px;}

.nav-pills .nav-link span {font-weight:700;}



.tabs-plans {
    background: transparent;
    color: #ffffff;
    border-radius: 4px;
    padding: 15px;
    text-align: left;
    margin-top: 0px;
    font-size: 16px;
    border: 2px solid #FFF8A1;
    margin-bottom: 20px;
	
}


.plan-group {
    background: url(../../assets/img/plan-bg.png) no-repeat center top;
    background-size: cover;
    border: 2px solid #12A1C5;
    padding: 0px;
    position: relative;
    margin-bottom: 50px;
    border-radius: 16px;
}


.last-row .plan-group {margin-bottom: 0px;}

.plan-group figure {
	margin: -50px auto 0px;
    text-align: center;
}


.plan-number {
    padding-bottom: 10px;
}


.plan-number h2 {
    font-size: 46px;
    text-align: right;
    text-transform: uppercase;
    margin-bottom: 0px;
    line-height: 26px;
    padding: 0px 8px;
    margin-top: 11px;
}

.plan-number h2 span {font-size:36px;}

.plan-number h2 span.daily {
	    font-size: 20px;
    display: block;
}


.plan-number p {
    font-size: 18px;
    line-height: 24px;
    margin-bottom: 0px;
    margin-top: 6px;
    padding: 6px 15px;
    border-left: 2px solid #FFC23F;
}

.plan-number p b {
	font-size:30px;
	font-weight:normal;
	display:block;
}

.number-inner p {
	font-size:14px;
	font-weight:500;
	text-transform:uppercase;
}

.number-inner p.percent_after {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 0px;
    margin-top: -5px;
}

.number-inner h2 span {
    font-size: 24px;
    margin-top: 0px;
    margin-left: 5px;
}

.plan-terms {
	font-size:18px;
	text-align:center;
	text-transform:uppercase;

}

.plan-terms span {font-weight:700; font-size:24px; display:block;}

.stat-box p {
    color: white;
    font-weight: 600;
    text-align: center;
    padding-top: 0px;
    margin-bottom: 0px;
    text-transform: uppercase;
}

.stat-box p span {
	display:block;
	font-weight:700;
	font-size:20px;
}

.profit-box {
	color: #ffffff;
    font-size: 11px;
    font-weight: 600;
    padding: 0.9rem 0.5rem;
    border-radius: 0px;
    margin: 0px 10px;
    text-transform: uppercase;
    background: url(../../assets/img/profit-big-bg.png) repeat-x left center;
    position: relative;
    margin-top: 0px;
    display: block;
    text-align: center;
    margin-bottom:10px;

}


.profit-box:after {
    content: '';
    position: absolute;
    right: -20px;
    top: 0px;
    width: 40px;
    height: 86px;
    background: url(../../assets/img/profit-arrow-left.png) no-repeat right center;
}


.profit-box:before {
    content: '';
    position: absolute;
    left: -20px;
    top: 0px;
    width: 40px;
    height: 86px;
    background: url(../../assets/img/profit-arrow-right.png) no-repeat left center;
}

.profit-box2 {

    background: url(../../assets/img/profit-big-bgo.png) repeat-x left center;


}


.profit-box2:after {

    background: url(../../assets/img/profit-arrow-lefto.png) no-repeat right center;
}


.profit-box2:before {

    background: url(../../assets/img/profit-arrow-righto.png) no-repeat left center;
}


.plan-select {
	color: #ffffff;
    font-size: 11px;
    font-weight: 600;
    padding: 0.9rem 0.5rem;
    border-radius: 0px;
    margin: 0px 10px;
    text-transform: uppercase;
    background: rgb(58,155,219);
    background: -moz-linear-gradient(top, rgba(58,155,219,1) 0%, rgba(24,118,197,1) 100%);
    background: -webkit-linear-gradient(top, rgba(58,155,219,1) 0%,rgba(24,118,197,1) 100%);
    background: linear-gradient(to bottom, rgba(58,155,219,1) 0%,rgba(24,118,197,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#3a9bdb', endColorstr='#1876c5',GradientType=0 );
    position: relative;
    margin-top: 0px;
    display: block;
    text-align: center;
    margin-bottom:10px;

}


.plan-select:after {
    content: '';
    position: absolute;
    right: -20px;
    top: 0px;
    width: 40px;
    height: 86px;
    background: url(../../assets/img/profit-arrow-leftb.png) no-repeat right center;
}


.plan-select:before {
    content: '';
    position: absolute;
    left: -20px;
    top: 0px;
    width: 40px;
    height: 86px;
    background: url(../../assets/img/profit-arrow-rightb.png) no-repeat left center;
}

.stat-box {
    background: url(../../assets/img/aff1.png) no-repeat center top;
    padding-top: 50px;
    min-height: 240px;
    padding-left: 15px;
    text-align: center;
    margin-top: 20px;
}

.aff-blue {
	background: url(../../assets/img/aff2.png) no-repeat center top;
	margin-top: 100px;
}

.aff-violet {
	background: url(../../assets/img/aff3.png) no-repeat center top;
}

.stat-box figure {margin-bottom:0px;}

.conditions {
    padding: 10px;
    background: rgb(52,164,192);
    background: -moz-linear-gradient(top, rgba(52,164,192,1) 0%, rgba(21,126,152,1) 46%, rgba(79,189,217,1) 100%);
    background: -webkit-linear-gradient(top, rgba(52,164,192,1) 0%,rgba(21,126,152,1) 46%,rgba(79,189,217,1) 100%);
    background: linear-gradient(to bottom, rgba(52,164,192,1) 0%,rgba(21,126,152,1) 46%,rgba(79,189,217,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#34a4c0', endColorstr='#4fbdd9',GradientType=0 );
    font-size: 24px;
    text-align: center;
    line-height: 28px;
    border-radius: 0px 0px 14px 14px;
	}


.conditions p {margin-bottom:0px;}


.calculator_block h1 {
    text-align: left;
    font-size: 30px;
    font-weight: 700;
    color: white;
    margin-top: 4px;
    line-height: 40px;
    letter-spacing: 0;
}

.calculator_block h1 b {font-weight:700; font-size:48px;}

.calculator_block h1 span {
	color:#ffc741;
}


.calculator-left {
	color: #464548;
    padding: 30px 20px 10px 0px;
    position: relative;
}

.calc-icon {
    position: absolute;
    background: #FEE500;
    padding: 29px;
    top: 50px;
    right: -60px;
    box-shadow: -15px 15px 0px 0px #243a78f2;
}


.calculator-left h1 {
    text-transform: uppercase;
    font-weight: 400;
    font-size: 30px;
    line-height: 46px;
    margin-bottom: 20px;
}

.calculator-left .col-form-label {font-size:24px;}

.calculator-left h1 span {
	color:#ba49ff;
}

.calculator-left  p {
	font-size:12px;
}

.calculator-right {
    padding-top: 50px;
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 600;
}




.plan-range {
    position: relative;
    padding-top: 10px;
}

.plan-range output {
	position: absolute;
    left: 0px;
    top: -10px;
}

.calculator-left input#money {

	font-size: 24px;
    color: #3b4347;
    font-weight: 600;
    background: transparent;
    display: inline-block;
    border: 2px solid #b746ff;
    padding: 0px 10px;

}

.input-group-text {
background: #000f30;
    color: white;
    border: 1px solid #183E8A;
    border-right: 0px;
    padding: 0.175rem 0.5rem;
    font-size:32px;
}

.plan-range input#money:focus {
	outline:none;
	border:0px;
}

.plan-range input {width:100%;}

.plan-drop select{
	background:transparent;
	border:0px;
	color:white;
}

.plan-drop select:focus {border:0px;}

.plan-drop option {
	background:#091F49;
	border:0px;
	color:white;
}


input[type="range"]::-webkit-slider-thumb {
     -webkit-appearance: none;
    background: rgb(225,11,182);
    background: -moz-linear-gradient(top, rgba(225,11,182,1) 0%, rgba(187,11,151,1) 100%);
    background: -webkit-linear-gradient(top, rgba(225,11,182,1) 0%,rgba(187,11,151,1) 100%);
    background: linear-gradient(to bottom, rgba(225,11,182,1) 0%,rgba(187,11,151,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#e10bb6', endColorstr='#bb0b97',GradientType=0 );
    opacity: 1;
    width: 20px;
    height: 20px;
    border-radius:100%;
}



.resultbox {
	background: url(../../assets/img/shape-sm.png) no-repeat center;
    text-align: center;
    display: inline-block;
    padding-left: 0px;
    font-size: 14px;
    min-width: 261px;
    margin-left: 0px;
        line-height: 32px;
    padding-top: 20px;
    min-height: 75px;
    text-transform: uppercase;

}

.resultbox p {
	color: #7ab4f4;
    font-size: 12px;
    text-transform: uppercase;
    font-weight: 600;
    margin-bottom: 0px;
    padding-right: 0px;
    text-align: left;
    line-height: 24px;
}

.resultbox h3 {
    margin-bottom: 0px;
    font-weight: 300;
    color: white;
    font-size: 36px;
    text-align: left;
    
}

.resultbox .form-control {
    margin-bottom: 0px;
    font-weight: 300;
    color: white;
    font-size: 36px;
    text-align: left;
    padding: 0px;
    background: transparent;
    border: 0px;
    line-height: 1rem;
}

.amount p {
 margin-bottom: 0px;
    margin-top: 10px;
}

.block-container {
background: #20438E;
    box-shadow: 20px 20px 0px 0px #1e377b8c;
    margin-left: -60px;
    z-index: 0;
    padding-left: 90px;
    padding-top: 30px;
    padding-right: 50px;
    padding-bottom: 40px;
}

.cert-info figure{
    position: relative;
    z-index: 1000000;
}

.cert-info figure img {max-width:100%;}

.cert-info {
    z-index: 1000;
    position: relative;
    }

.divider {
    height: 2px;
    background: #ffc741;
}

.guarantee {
	text-align:center;
}


.security-row h4 {
	color:#778693;
	font-size:18px;
	text-transform:uppercase;
}

.security-row h3 {
	color:#315ac8;
	font-weight:700;
	font-size:30px;
}


.guarantee h3 {
	color:#315ac8;
	font-weight:700;
	font-size:18px;
}


.security-row p {
	color:#6e7276;
	font-size:14px;
	line-height:26px;
}


.security-row  p.mute {
color: #afb2ba;
    margin-bottom: 0.2rem;
}


.guarantee1 {
	margin-top: -150px;
}

.guarantee2 {
	margin-top: -110px;
}

.calculateresults {
    padding: 20px;
    border-radius: 6px;
    position: relative;
}


.calculator-block label {
    font-size: 18px;
    text-transform: uppercase;
    font-family: 'sansationbold', sans-serif;
    margin-bottom: 5px;
    margin-top: 30px;
    display: block;

}

.resultbox input[type="text"] {

    background: transparent;
    color: #ffffff;
    font-size: 24px;
    font-weight: 400;
    border: 0px;
    text-align: center;
    font-family: 'sansationbold', sans-serif;

}

.resultbox span {
	    font-family: 'sansationbold', sans-serif;
	    font-size:30px;
	    color:#20f078;
}

.affiliate_area .resultbox span {
	font-size:18px;
	display:block;
}


.resultbox label {

    text-transform: uppercase;
    font-size:12px;
     font-weight: 700;
    
}

.affiliate_area .resultbox p {color:white; font-size:14px; font-family: 'sansationbold', sans-serif; }

.affiliate_area .resultbox {
    background: url(../../assets/img/shape-sm.png) no-repeat center;
    text-align: center;
    display: inline-block;
    padding-left: 0px;
    font-size: 14px;
    min-width: 261px;
    margin-left: 0px;
    line-height: 32px;
    padding-top: 20px;
    min-height: 75px;
    text-transform: uppercase;
    padding: 20px 38px 15px;
}


.right_info h1 {
    font-weight: 300;
    text-transform: uppercase;
    font-size: 40px;
    margin-top: 60%;
}

.right_info h1 span {font-weight:700;}

.right_info  a {
    background: #2C8FC5;
    color: white;
    text-decoration: none;
    padding: 22px 30px;
    margin-top: 20px;
    display: inline-block;
    border-radius: 4px;
    text-transform: uppercase;
    font-weight: 600;
    font-size: 14px;
}

.right_info {padding-left:20px; position:relative; z-index:1000;}

.right_info  a:hover {background:#2F9EDA;}

.about-right-image {
    position: absolute;
    z-index: 0;
    right: 0px;
    bottom: -202px;
}

.block p {
	color:#79a5e9;
	text-transform:uppercase;
	font-size:14px;
	margin-bottom:0px;
	margin-top:10px;
}

.block h4 {
	font-weight:600;
	font-size:14px;
	
}

.payment-info {padding-top:60px;}

.payment-info h4 {
	color:#434e5b;
	font-size:18px;
	font-weight:600;
}

.payment-info h2 {
	color:#1755bb;
	font-size:36px;
	font-weight:700;
}

.payment-info p {font-size:16px; line-height:26px;}

.company_info h2 {
	background: url(../../assets/img/feat_dots.png) no-repeat right center;
	padding-right:30px;
	font-size:24px;
	font-weight:600;
	text-transform:uppercase;
	padding:20px 30px 20px 20px;
}

.company_info h2 span {
	font-weight:700;
	color:#ffe100;
	display:block;
	font-size:30px;
}

.company_info .block h2 {
	font-size:36px;
	font-weight:800;
	padding-left:0px;
	padding-top:0px;
	padding-bottom:0px;
	background:transparent;
}

.stats_title {
	background: url(../../assets/img/stats_title_bg.png) no-repeat left center;
	padding:20px 0px;
}

.stats_title h1 {
    font-size: 48px;
    text-transform: uppercase;
    font-weight: 500;
    text-align: center;
    color: white;
}




.footer {

}

.processors p {
	font-weight:500;
	font-size:14px;
	text-transform:uppercase;
}





.footer-content a {color:white; text-decoration:none; padding:0px 3px 2px;}

.footer-content a.number {color:#E8970A;}

.footer-copy {text-align:left;}


.footer-copy p {
    font-size: 14px;
    margin-bottom: 0px;
    text-align: left;
    color: white;
    font-weight: 400;
    text-transform: uppercase;
}

.footer-copy p span {font-size:18px; font-weight:500; }

.footer-copy p font {

color:#1bc16f;
font-weight:600;

}

.footer ul li {

    list-style: none;
    color: white;
    text-decoration: none;
    font-size: 12px;
    font-weight: 400;
    line-height: 28px;
    border: 0px;
    border-radius: 0px;
    position: relative;
    text-transform: uppercase;
    margin-bottom: 3px;

}



.footer h4 {
    color: #f7bb1a;
    font-weight: 400;
    font-size: 14px;
    text-transform: uppercase;
    margin-top: 10px;
    background: url(../../assets/img/yellow-line.png) no-repeat right center;
    margin-bottom: 20px;
}
.footer ul {padding-left:0px;}

.footer ul li a {
    color: white;


}

.footer-info p {
    font-size: 14px;
    color: #ffffff;
    margin-bottom: 0px;
    margin-top: 5px;
    vertical-align: middle;
} 

.footer-info p a { color: #ffffff; font-size:12px;}




.footer figure {
    border-radius: 6px;
    padding: 5px;
    text-align: center;
    margin-bottom: 0px;

}



.footer-info span {color:#b543ff;}


/*Subpages*/

.description {
	line-height:28px;
}

.about-sub .feat-block p {
    font-size: 15px;
    line-height: 28px;
    text-align: justify;
    color: #2b3145;
}

.about-sub img {max-width:600px;}

.register-form {
    border-radius: 6px;
	/* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#01a1ed+0,0253d2+100 */
	background: rgb(1,161,237); /* Old browsers */
	background: -moz-linear-gradient(45deg, rgba(1,161,237,1) 0%, rgba(2,83,210,1) 100%); /* FF3.6-15 */
	background: -webkit-linear-gradient(45deg, rgba(1,161,237,1) 0%,rgba(2,83,210,1) 100%); /* Chrome10-25,Safari5.1-6 */
	background: linear-gradient(45deg, rgba(1,161,237,1) 0%,rgba(2,83,210,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#01a1ed', endColorstr='#0253d2',GradientType=1 ); /* IE6-9 fallback on horizontal gradient */
    padding: 30px;
    box-shadow: 0px 0px 10px #0000001f;
}
.redirect-body {
    background: #3B0658 url(../../assets/img/banner-bg.jpg) no-repeat left top;
    background-attachment: fixed;
    position: relative;
    overflow: hidden;
    padding-bottom: 130px;
    color:white;
}



.about-row .guarantee p {
	color:white;
}

.about-row  .guarantee h3 {
    color: #CEE1F2;
    
    }


.start-block {
    margin: 10px 0px;
}

.start-top {
	padding: 10px 20px;
    border-radius: 6px;
    background: white;
    filter: drop-shadow(-4px 10px 10px rgba(6, 19, 63, 0.2));
}


.start-top p {margin-bottom:5px;}

.accordion-heading {
    color: #ffffff;
    background: rgb(58,155,219);
    background: -moz-linear-gradient(top, rgba(58,155,219,1) 0%, rgba(24,118,197,1) 100%);
    background: -webkit-linear-gradient(top, rgba(58,155,219,1) 0%,rgba(24,118,197,1) 100%);
    background: linear-gradient(to bottom, rgba(58,155,219,1) 0%,rgba(24,118,197,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#3a9bdb', endColorstr='#1876c5',GradientType=0 );
    font-size: 16px;
    font-weight: 700;
    text-transform: uppercase;
    padding: 15px 20px;
    border-radius: 6px;
    margin-bottom: 10px;
}

.f36 {font-size:36px;}

.start-block h2 {
    color: #1361c6;
    font-size: 24px;
    font-weight: 400;
    text-transform: uppercase;
    line-height: 64px;
}


.subpage_wrap .start-block h2 {
    color: #1361c6;
    font-size: 20px;
    font-weight: 600;
    margin-top: 0px;
    line-height: 40px;
    margin-bottom: 0px;
    text-transform:none;
}

.register-form h2 {
    color: #ffffff;
    text-transform: uppercase;
    font-family: 'sansationbold', sans-serif;
    font-size: 20px;
    margin: 10px 0px 20px;
    background: url(../../assets/img/yellow-line.png) no-repeat right center;
}

.register-form .form-control, .inpts {
    padding: 0.575rem 0.75rem;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    color: #59697a;
    background-color: #ffffff;
    border: 1px solid #2a5ec8;
    border-radius: 0.25rem;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    border-radius: 4px;
}

.account-table .inpts {
	background: #071635;
	color:white;
}

.signup-title {
    text-align: center;
    font-size: 36px;
    font-weight: 700;
    color: #E10BB7;
    line-height: 30px;
}
.signup-title span {
	display:block;
	text-transform:uppercase;
	font-size:16px;
	letter-spacing:3px;
	color:#8997AB;
	font-weight:400;
}

.refinfos {
    background: rgb(27,69,151);
    background: -moz-linear-gradient(45deg, rgba(27,69,151,1) 0%, rgba(30,80,180,1) 100%);
    background: -webkit-linear-gradient(45deg, rgba(27,69,151,1) 0%,rgba(30,80,180,1) 100%);
    background: linear-gradient(45deg, rgba(27,69,151,1) 0%,rgba(30,80,180,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1b4597', endColorstr='#1e50b4',GradientType=1 );
    color: #ffffff;
    border-radius: 4px;
    padding: 15px;
    text-align: left;
    margin-top: 10px;
    font-size: 16px;
    border: 0px;
}

.refinfos .form-control {border:0px;}

.refinfos h2 {
	font-size: 18px;
	font-weight:400;
}

.login-container h2 {
	color:#5968BE;
	font-size:1.5rem;
}


.login-container .about-info h2 {
    font-size: 36px;
    font-family: 'Koulen', sans-serif;
    font-weight: 400;
    margin-top: 20px;
    line-height: 42px;
    color:white;
}


.login-container2 img {max-width:100%;}

.accordion-button {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0.7rem 1.25rem;
    font-size: 1rem;
    color: #ffffff;
    text-align: left;
    background: rgb(1,161,237);
    background: -moz-linear-gradient(45deg, rgba(1,161,237,1) 0%, rgba(2,83,210,1) 100%);
    background: -webkit-linear-gradient(45deg, rgba(1,161,237,1) 0%,rgba(2,83,210,1) 100%);
    background: linear-gradient(45deg, rgba(1,161,237,1) 0%,rgba(2,83,210,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#01a1ed', endColorstr='#0253d2',GradientType=1 );
    border: 0;
    border-radius: 4px;
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 600;
}
.accordion-item {
    background:transparent;
    border: 0px;
    margin-bottom: 5px;
    border-radius: 6px;
    font-size:14px;
}

.accordion-button i {
	color: #3CACC8;
	margin-right:6px;
}


.accordion-button:not(.collapsed) {
    color: #ffffff;
    background-color: #0e2c57;
    box-shadow: inset 0 -1px 0 rgb(0 0 0 / 13%);
}




.form-footer {
    border-top: 1px solid #66b7e8;
    padding: 20px 0px 0px;
    margin-top: 20px;
    text-align: center;
    font-weight: 400;
    font-size: 18px;
}

.form-footer a {color:white;}

.card, .login-container {
	background: rgb(9,125,225);
    background: -moz-linear-gradient(left, rgba(9,125,225,1) 0%, rgba(24,82,184,1) 100%);
    background: -webkit-linear-gradient(left, rgba(9,125,225,1) 0%,rgba(24,82,184,1) 100%);
    background: linear-gradient(to right, rgba(9,125,225,1) 0%,rgba(24,82,184,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#097de1', endColorstr='#1852b8',GradientType=1 );
    border-radius: 10px;
    box-shadow: 0 1.2rem 2rem rgb(10 31 68 / 10%);
    padding: 0px;
    border: 0px;
}

.login-title {
    background: rgb(184,95,251);
    background: -moz-linear-gradient(left, rgba(184,95,251,1) 0%, rgba(235,130,255,1) 100%);
    background: -webkit-linear-gradient(left, rgba(184,95,251,1) 0%,rgba(235,130,255,1) 100%);
    background: linear-gradient(to right, rgba(184,95,251,1) 0%,rgba(235,130,255,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#b85ffb', endColorstr='#eb82ff',GradientType=1 );
    font-size: 30px;
    font-weight: 400;
    color: white;
    border-radius: 9px 9px 0px 0px;
    text-transform: uppercase;
    padding: 16px 20px;
    text-align: center;

}

.login-title p {
    font-size: 20px;
    font-family: 'Poppins', sans-serif;
    text-transform: none;
    line-height: 30px;
}



.login-container2 .form-label {color:white; font-weight:600;}

select {
    padding: 0.375rem 0.35rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #606978;
    background-color: white;
    background-clip: padding-box;
    border: 1px solid #87b1ec;
    border-radius: 4px;
    font-size: 16px;
}

.form-control:focus {
    color: #5B6578;
    background-color: #F8F9FB;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgb(13 110 253 / 25%);
}


.card-top figure {margin-bottom:0px;}

.login-title span {
	    color: #ffb531;
}

.table td {text-align:left;}



table {width:100%;}

table td, table tr {
    font-size: 14px;
    padding: 5px;
    border-bottom: 1px solid #c1d4eb78;
}

.account-table td, .account-table th {
    font-size: 16px;
    padding: 8px;
    border-bottom: 1px solid #25558f;
    color: #ffffff;
    background: transparent;
}

table td.inheader {
    color: white;
    border-bottom: 1px solid #FBB723;
    font-family: 'sansationbold', sans-serif;
    background: #071635;

}

.btn-secondary {
    color: #fff;
    background-color: #0b2d60;
    border-color: #0b2d60;
}

.tabs-plans .btn-check:active+.btn-secondary, .tabs-plans .btn-check:checked+.btn-secondary, .tabs-plans .btn-secondary.active, .tabs-plans .btn-secondary:active, .tabs-plans .show>.btn-secondary.dropdown-toggle {
    color: #fff;
    background: #18BB63;
}

.middle-account {color:white;}

.account-table table {
    margin-bottom: 10px;
    border: 1px solid #25558f;
}
.account-table h3 {
    color: #ffffff;
    font-weight: 400;
    font-size: 20px;
    text-transform: uppercase;
    margin-top: 10px;
    background: url(../../assets/img/yellow-line.png) no-repeat right center;
    margin-bottom: 20px;
}

.subpage_account .plan-terms{
	font-size:14px;
}

.subpage_account .return img {
    display: inline-block;
    margin: 0px;
}

.subpage_account .plan-number {
	padding:0px;
	margin:0px;
	padding-top:8px;
}

.subpage_account .plan-number h2 {
	font-size:24px;
}


.subpage_account .subpage_wrap {
    background:transparent;
    border-radius: 6px;
    border: 0px;
}


.member-menu {
    padding-bottom: 10px;
}

.alert-info {
    color: #ffffff;
    background-color: #4173d9;
    border-color: #4173d9;
}

.plan-selection h2 {
            font-size: 18px;
    font-family: 'sansationbold', sans-serif;
    
}

.plan-details h2 {
            background: rgb(244,151,12);
    background: -moz-linear-gradient(left, rgba(244,151,12,1) 0%, rgba(254,182,32,1) 100%);
    background: -webkit-linear-gradient(left, rgba(244,151,12,1) 0%,rgba(254,182,32,1) 100%);
    background: linear-gradient(to right, rgba(244,151,12,1) 0%,rgba(254,182,32,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f4970c', endColorstr='#feb620',GradientType=1 );
    color:white;
        font-size: 16px;
    padding: 0.6rem 0.6rem;
    text-transform:uppercase;
    border-radius:6px;
    
}

.plan-details p {
    background:#091B3F;
    padding:5px 15px;
    border-radius:6px;
    font-size:12px;
    margin-bottom:2px;
    
}

.account-block1 {
	background: rgb(255,152,5);
    background: -moz-linear-gradient(45deg, rgba(255,152,5,1) 0%, rgba(255,194,42,1) 100%);
    background: -webkit-linear-gradient(45deg, rgba(255,152,5,1) 0%,rgba(255,194,42,1) 100%);
    background: linear-gradient(45deg, rgba(255,152,5,1) 0%,rgba(255,194,42,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ff9805', endColorstr='#ffc22a',GradientType=1 );
    border-radius:6px;
    color:white;
    padding:20px;
}


.page-link {
    position: relative;
    display: block;
    color: #70a0e7;
    text-decoration: none;
    background-color: #10337a;
    border: 1px solid #0156ab;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}

.account-block2 {
    background: #ffffff61;
    color: #2b3e73;
    border-radius: 4px;
    padding: 15px;
    text-align: left;
    margin-top: 10px;
    font-size: 16px;
    border: 1px solid #fe9f8eb3;
}

.account-block2 span {
	color: #0d73d8;
    font-size: 18px;
    font-weight:700;
}

.account-block {
	background: -moz-linear-gradient(top, rgba(15,28,60,0.86) 0%, rgba(8,48,102,0.86) 50%, rgba(15,28,60,0.86) 100%);
    background: -webkit-linear-gradient(top, rgba(15,28,60,0.86) 0%,rgba(8,48,102,0.86) 50%,rgba(15,28,60,0.86) 100%);
    background: linear-gradient(to bottom, rgba(15,28,60,0.86) 0%,rgba(8,48,102,0.86) 50%,rgba(15,28,60,0.86) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#db0f1c3c', endColorstr='#db0f1c3c',GradientType=0 );
    border-radius: 6px;
    padding: 20px;
    border: 2px solid #f0c84c;

}

.account-block2 .block-info p {
    font-size: 15px;
    margin-bottom: 5px;
}


.mem-block {
    background: rgb(27,69,151);
    background: -moz-linear-gradient(45deg, rgba(27,69,151,1) 0%, rgba(30,80,180,1) 100%);
    background: -webkit-linear-gradient(45deg, rgba(27,69,151,1) 0%,rgba(30,80,180,1) 100%);
    background: linear-gradient(45deg, rgba(27,69,151,1) 0%,rgba(30,80,180,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1b4597', endColorstr='#1e50b4',GradientType=1 );
    color: #ffffff;
    border-radius: 4px;
    padding: 15px;
    text-align: left;
    margin-bottom: 10px;
    font-size: 16px;
}

.mem-block figure {margin-bottom:0px;}

.mem-block p {margin-bottom:0px;}

.mem-block span {
display: block;
    color: #20f078;
    font-size: 18px;
    font-weight: 400;
    font-family: 'sansationbold', sans-serif;
}
.account-block3 {
/* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#1b4597+0,1e50b4+100 */
background: rgb(27,69,151); /* Old browsers */
background: -moz-linear-gradient(45deg, rgba(27,69,151,1) 0%, rgba(30,80,180,1) 100%); /* FF3.6-15 */
background: -webkit-linear-gradient(45deg, rgba(27,69,151,1) 0%,rgba(30,80,180,1) 100%); /* Chrome10-25,Safari5.1-6 */
background: linear-gradient(45deg, rgba(27,69,151,1) 0%,rgba(30,80,180,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1b4597', endColorstr='#1e50b4',GradientType=1 ); /* IE6-9 fallback on horizontal gradient */
    border:0px;
    color: white;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    line-height: 36px;
}

.plan-details {color:white;}

.account-block3 figure {
    margin: 0px;
}

.block-but a {
    color: white;
    background: rgb(240,199,82);
    background: -moz-linear-gradient(top, rgba(240,199,82,1) 0%, rgba(228,165,9,1) 46%, rgba(251,197,90,1) 100%);
    background: -webkit-linear-gradient(top, rgba(240,199,82,1) 0%,rgba(228,165,9,1) 46%,rgba(251,197,90,1) 100%);
    background: linear-gradient(to bottom, rgba(240,199,82,1) 0%,rgba(228,165,9,1) 46%,rgba(251,197,90,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f0c752', endColorstr='#fbc55a',GradientType=0 );
    border-radius: 36px;
    padding: 4px 25px;
    font-size: 20px;
}

.block-info p {
    font-size: 24px;
}
.block-info p span {
    font-size: 24px;
    display: block;
}

.account-block3 p {
    margin-bottom: 0px;
    text-align: right;
    font-size: 16px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 24px;
}

.account-block3 p span {
font-size: 24px;
    color: #ffbd25;
    font-weight: 400;
}
.select-button .radio {
    color: white;
    width: auto;
    padding: 8px 10px;
    background: #5677d1;
    margin: 10px;
    display: inline;
    border-radius: 36px;
    text-align: center;
    font-weight: 600;
    font-size: 11px;
    text-transform: uppercase;
}


.select-button input[type=radio] {
	display: none;
}
.select-button  input[type="radio"]:checked + .radio {
  color: white;
    width: auto;
    padding: 8px 10px;
    background: #4BA9F3;
    margin: 10px;
    display: inline;
    border-radius: 36px;
    text-align: center;
    font-weight: 600;
    font-size: 11px;
    text-transform: uppercase;
}


.subpage_wrap {
    background: #071635 url(../../assets/img/about-bg.jpg) no-repeat center top;
    padding: 60px 0px;
    border-top: 2px solid #FEF8A2;
    background-size: cover;
}

.login-container img {max-width:100%;}



.cert-image {
padding: 0px;
    border: 2px solid #e7e6f4;
    margin: 0px 30px;
    background: white;
    text-align:center;
}

.nav-menu {
padding: 0px;
    margin: 0px;
    width: 100%;
    text-align: center;
    }


.nav-menu  a{
    margin-left: 5px;
    color: #FEB51F;
    padding: 10px 10px;
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 1px;
    display: block;
    background: url(../../assets/img/acc-menu.png) no-repeat center;
    text-transform: uppercase;
    display: block;
    position: relative;
    margin-right: 5px;
    text-align: center;
    line-height: 30px;
    min-width: 190px;
    font-family: 'sansationbold', sans-serif;
}

.nav-menu  a:hover {
	color:white;
}



.nav-menu i {float:right; color:#498BEB;}


.nav-menu li {
    list-style: none;
    display: inline-block;

}


.news-block {
    
            border-radius: 6px;
    background: rgb(1,161,237);
    background: -moz-linear-gradient(45deg, rgba(1,161,237,1) 0%, rgba(2,83,210,1) 100%);
    background: -webkit-linear-gradient(45deg, rgba(1,161,237,1) 0%,rgba(2,83,210,1) 100%);
    background: linear-gradient(45deg, rgba(1,161,237,1) 0%,rgba(2,83,210,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#01a1ed', endColorstr='#0253d2',GradientType=1 );
    padding: 30px;
    box-shadow: 0px 0px 10px #0000001f;
        margin-bottom: 20px;
    
}

.news-title {
    font-size: 22px;
    color: white;
    font-weight: 500;
    margin-bottom: 10px;
    display: block;
    
}


.news-block i {
        padding-top: 10px;
    display: block;
    text-align: right;
    border-top: 1px dashed #FEF8A2;
    margin-top: 10px;
    
}

.user-block {
    padding: 20px 0px;
    text-align: center;
    font-size: 18px;
    color:white;

}

.user-block span {
    color: #4EC0FF;
    display:block;
    font-weight:700;
    margin-bottom:6px;
}

.user-block .btn {
border: 2px solid white;
    font-size: 12px;
    text-transform: uppercase;
}

.breadcrumbs {
    text-align: center;
    color: #1a79c7;
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 20px;
}


.breadcrumbs a {color: #1a79c7;}


.account-top .resultbox {padding-top:15px;}

.account-top .resultbox p span {

    font-size: 16px;
    display:block;
    font-family: 'sansationbold', sans-serif;

}

.account-top .resultbox p {
	    color: white;
    font-size: 14px;
    font-family: 'sansationregular', sans-serif;
    text-transform: none;
    text-align:center;
}


.member-content .plan-box {
    background: url(../../assets/img/plan-orange.png) no-repeat center top;
    padding-top: 80px;
    min-height: 300px;
    background-size: 100%;
    padding-left: 10px;
    text-align: center;
}

.member-content .box-blue {
    background: url(../../assets/img/plan-blue.png) no-repeat center top;	
        background-size: 100%;
}

.member-content .plan-box h3 {
    font-size: 36px;
    }

.loader {
  width: 68px;
  height: 68px;
  display: inline-block;
  position: relative;
}
.loader::after,
.loader::before {
  content: '';  
  box-sizing: border-box;
  width: 68px;
  height: 68px;
  border-radius: 50%;
  border: 2px solid #FFF;
  position: absolute;
  left: 0;
  top: 0;
  animation: animloader 2s linear infinite;
}
.loader::after {
  animation-delay: 1s;
}

@keyframes animloader {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}
  
.member-content {
    border-radius: 16px;
	/* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#0c1f48+0,122d61+100 */
	background: rgb(12,31,72); /* Old browsers */
	background: -moz-linear-gradient(45deg,  rgba(12,31,72,1) 0%, rgba(18,45,97,1) 100%); /* FF3.6-15 */
	background: -webkit-linear-gradient(45deg,  rgba(12,31,72,1) 0%,rgba(18,45,97,1) 100%); /* Chrome10-25,Safari5.1-6 */
	background: linear-gradient(45deg,  rgba(12,31,72,1) 0%,rgba(18,45,97,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#0c1f48', endColorstr='#122d61',GradientType=1 ); /* IE6-9 fallback on horizontal gradient */

    padding: 20px 30px;
    box-shadow: 0px 0px 10px #0000001f;
}


.page-subtitle {
    color: #ffffff;
    font-weight: 400;
    font-size: 24px;
    text-transform: uppercase;
    margin-top: 10px;
    background: url(../../assets/img/yellow-line.png) no-repeat right center;
    margin-bottom: 20px;
}

.link {
	color:white;
	font-size:24px;
}

.referral-block .btn {
    font-size: 12px;
    text-transform: uppercase;
    line-height: 27px;
}

.referral-block .form-control {    padding: 0.275rem 0.75rem;}

    
@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
} 


.wrd-but {width:200px; font-size:12px; text-align:left;}

.wrd-but img {width:32px;}

.account-table .btn {font-size:14px;}

@media (max-width:1025px) {
	.nav-tabs > li > a {
		font-size: 20px;
		line-height: 40px;
	}
	.useractivitywrap .heading {
		margin: 0 0 40px;
	}
	.tabwrap {
		padding: 0;
	}
	.helpcenter a, .support a {
		font-size: 10px;
	}
	.lastwrap .head ul li, .lastwrap .infos ul li {
		padding: 5px;
		font-size: 12px;
	}
	.lastwrap .infos ul li {
		font-size: 12px;
	}

	.headblk img {max-width:100%;}
	
.headblk.two {
    right: 0px;
    top: 40px;
    left: 20px;
}

.headblk.four {
	left: 0px;
    top: -20px;
}


.top-stats {
    margin-top: 0px;
    padding: 10px;
    border-radius: 6px;
    padding-bottom: 20px;
}

	.bannerwrap {
    padding: 0px 0 30px;
    margin-top: 0px;
    background: url(../../assets/img/banner-bg.png) no-repeat left bottom;
	}
	.bannerleft h1 {
		font-size: 42px;
		line-height: 42px;
	}
	.bannerleft p {
		font-size: 16px;
		line-height: 27px;
		margin: 0 0 30px;
	}
	.calvalbox .infos h4 {
		font-size: 40px;
		line-height: 40px;
	}
	.calvalbox .infos h4 span {
		font-size: 28px;
	}
	.calvaluewrap {
		padding: 30px 25px;
		border-radius: 15px;
	}
	.planbox {
		padding: 15px 15px 30px;
		margin: 0 0 20px;
	}
	.plantop .val {
		font-size: 52px;
		line-height: 52px;
	}
	.plantop .daily {
		font-size: 18px;
		line-height: 24px;
	}
	.plantop .daily br {
		display: none;
	}
	.planbox .minmax ul {
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
	}
	.planbox .minmax ul li {
		padding: 0 0 0 24px;
		margin: 0 0 5px;
		border-right: none;
		width: 100%;
	}
	.welsomemsg h1 {
		font-size: 28px;
		line-height: 34px;
	}
	.welsomemsg p {
		font-size: 16px;
		line-height: 28px;
	}
	.statwrap h4 {
		font-size: 28px;
	}
	.statwrap p {
		font-size: 16px;
	}
	.benefitswrap,.useractivitywrap,.welsomemsg {
		padding: 50px 0;
	}
	.benefitswrap .heading {
		margin: 0 0 40px;
	}
	.benefitswrap .heading h2,.useractivitywrap .heading h2 {
		font-size: 50px;
	}
	.benefitswrap .heading p,.useractivitywrap .heading p {
		font-size: 20px;
		line-height: 32px;
	}
	.benwrap {
		padding: 20px 0 0 0;
	}
	.bentext h3 {
		font-size: 26px;
		padding: 0 0 15px;
	}
	.bentext p {
		font-size: 16px;
		line-height: 26px;
	}
	.affiright h2, .affiright h3 {
		font-size: 28px;
		line-height: 38px;
	}
	.affiright p {
		font-size: 16px;
		line-height: 24px;
	}
	.affiplan h3 {
		font-size: 38px;
	}
	.affiplan p {
		font-size: 20px;
	}
	.flinkbox h3 {
		font-size: 24px;
	}
	.flinkbox a.viewcomp {
		font-size: 12px;
	}
}
@media (max-width:992px) {
}
@media (max-width:812px) {

.member-content .plan-box {padding-top:110px;}

.yellow-box {display:none;}


.telegram-top {
    position: fixed;
    left: 0px;
    top: 20%;
    background: #0D2549 url(../../assets/img/telegram-top.png) no-repeat center;
    width: 67px;
    height: 211px;
    border: 2px solid #039BE5;
    z-index: 1000000;
    background-size: 45px auto;
}

.why-box-text {padding-top:30px;}

.affiliate_area .resultbox {
	padding: 20px 80px 15px;
}

.banner-middle h2 {
    text-transform: uppercase;
    font-size: 18px;
    margin-bottom: 5px;
}

#banner-img {
    position: relative;
    margin: 0 auto;
    margin-top: -20px;
}

#banner-img img {max-width:100%;}

#banner-img {
    position: relative;
    margin: 0 auto;
    margin-top: 20px;
    height: 300px;
}

.offset-md-1 {
    margin-left: 0px;
}

.affiliate_area img {max-width:100%;}

.plan-box {
    padding-top: 80px;
    min-height: 400px;
}

.banner-middle {
    padding: 20px 10px;
    text-align: center;
}

.banner-middle p {
    font-size: 20px;
    padding: 5px 0px;
    margin-bottom: 0px;
}

.banner-middle h2 {
    text-transform: uppercase;
    font-size: 20px;
}

.payment-block img {
    max-height: 55px;
}

.return {
	padding-left: 75px;
}

.aff-blue {margin-top:10px;}

.statistics img {max-width:100%;}

.price {
    display: inline-block;
    padding: 10px 3px 0px;
}

.prices {
    margin-bottom: 15px;
}

.account-table td, .account-table th {display:block;}

.guarantee1 {
    margin-top: 10px;
}

.guarantee2 {
    margin-top: 10px;
}

.stat-middle p {
    font-size: 18px;
    margin-bottom: 0px;
    text-align: center;
}


.affiliate_img figure {margin-top:20px;}

.stat-bg {margin-top:10px;}

.last-row .plan-group {  margin-bottom: 50px;}

.features-top .d-flex {
	display:block!important;
}

.plan span {
	background: rgb(201,209,231);
    background: -moz-linear-gradient(left, rgba(201,209,231,1) 0%, rgba(227,232,243,1) 100%);
    background: -webkit-linear-gradient(left, rgba(201,209,231,1) 0%,rgba(227,232,243,1) 100%);
    background: linear-gradient(to right, rgba(201,209,231,1) 0%,rgba(227,232,243,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#c9d1e7', endColorstr='#e3e8f3',GradientType=1 );
    color: #0d73d8;
    padding: 0px 0px 0px 15px;
    border-radius: 36px 0px 0px 36px;
    text-transform: uppercase;
    font-weight: 600;
    margin-right: 0px;
    line-height: 60px;
    display: inline-block;
    width: 170px;
    margin-left: 10px;
}

.statistics {
    padding: 0px 0px;
    background: #00133D url(../../assets/img/aff-bg.png) no-repeat center bottom;
    padding-bottom: 60px;
    background-size: 100%;
}

.features-bottom {display:block!important;}

.page-title {
    font-size: 24px;
    text-transform: uppercase;
    color: white;
    font-weight: normal;
}

.buttons {
    margin-top: 10px;
    text-align: center;
}

.buttons .btn-primary {font-size:10px;}

.features-bottom p {
    margin-bottom: 0px;
    line-height: 24px;
    padding: 10px;
}

.security h1 {font-size:20px;}

.coins-container {
	position: relative;
    padding-top: 600px;
    overflow:hidden;
}

.coins-container-top {
	padding-top: 800px;
}

.days-online {
	    margin-left: 10px;
}

.banners-bottom a {margin-bottom:10px;}

.navbar-toggler {
    padding: 0.65rem 0.75rem;
    font-size: 1.25rem;
    line-height: 1;
    background: rgb(244,151,12);
    background: -moz-linear-gradient(left, rgba(244,151,12,1) 0%, rgba(254,182,32,1) 100%);
    background: -webkit-linear-gradient(left, rgba(244,151,12,1) 0%,rgba(254,182,32,1) 100%);
    background: linear-gradient(to right, rgba(244,151,12,1) 0%,rgba(254,182,32,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f4970c', endColorstr='#feb620',GradientType=1 );
    border: 1px solid transparent;
    border-radius: 0.25rem;
    transition: box-shadow .15s ease-in-out;
    margin: 0px auto;

}

.top-part {
    background: #02152D url(../../assets/img/banner-bg.jpg) no-repeat left bottom;
    background-attachment: fixed;
    position: relative;
    overflow: hidden;
    padding-bottom: 280px;
}

.home-but {display:none;}

.navbar-expand-lg .navbar-collapse {
    margin-top: 10px;
}

.crypto-prices .col {width:50%; margin-bottom:5px;}

.headblk.ten {
    left: -10px;
    top: 0px;
}

.top-wrapper {
    background: #fdfcfd url(../../assets/img/banner-bg.jpg) no-repeat center;
    background-attachment: fixed;
    position: relative;
}


.bannercontent {
position: relative;
    width: 100%;
    background: url(../../assets/img/banner-img.png) no-repeat right bottom;
    padding-bottom: 380px;
    background-size: 100%;
}

.about-right-image {
    position: absolute;
    z-index: 0;
    right: 0px;
    bottom: 0px;
}

.headblk.three {
	left: 10px;
    top: 460px;
}

.headblk.one {
    top: 220px;
    left: 0px;
}

.headblk.five {
left: -20px;
    top: 230px;
}

.top-road {background:transparent;}

.about-bg h2 {
font-size: 40px;
    line-height: 50px;
    margin-top:0px;
}

.right_info h1 { margin-top: 20%;}

.stats_title h1 {
    font-size: 42px;
    text-transform: uppercase;
    font-weight: 700;
}

.features-top h2 {
	font-size: 30px;
    text-align: center;
    }

.about-bg {
	background: url(../../assets/img/about_img.png) no-repeat center bottom;
    padding-bottom: 0px;
}


.plans-title span {
    color: #333f60;
    font-size: 30px;
    border-left: 0px;
    display: inline-block;
    margin-left: 0px;
    padding-left: 0px;
    text-align: center;
}

.plan-info {
    padding: 10px 10px;
    text-align: center;
}

.plan-info p {
    font-weight: 600;
    font-size: 14px;
    color: white;
    line-height: 42px;
    margin-bottom: 0px;
}

.button-group {
    padding: 10px 0px 20px;
    margin-top: 0px;
    text-align: center;
}

 .affiliate_text {
    padding-left: 0px;
    padding-top: 20px;
}       

.plans-title {
    font-size: 36px;
    line-height: 40px;
    text-align: center;
}

.nav-pills .nav-link {border-radius: 16px; margin-right:0px;}

.nav-pills .nav-link2 {
    margin-left: 0px;
}

.plans-container {
    margin-top: 0px;
}



.calculator-left {

}





.calc-icon {top:30px;}


.calculator-right {
    padding-left: 20px;
    padding-right: 30px;
    padding-top: 30px;
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 600;
    margin-top: 0px;
    padding-bottom: 20px;
}

.resultbox p {
    margin-bottom: 0px;
}

.plan-range {
    position: relative;
    padding-top: 10px;
}

.plan-range output {top:-5px;}

.text-block {
	padding: 0px 15px;
}


	.d-flex-mob {display: block!important;}



.top-logo-container p {text-align:center; margin-right:0px;}

	.profit {padding-top:0px; padding-left:3px;}
	

.bannerwrap h1 {
    font-weight: 400;
    color: #ffffff;
    font-size: 42px;
    margin-bottom: 20px;
    line-height: 52px;
    margin-top: 20px;
}

.bannerwrap h1 span {
	    font-size: 50px;
}

.navbar-brand img {
	    max-width: 300px;
}

.navbar-brand {
    margin-top: 30px;
    display: block;
    text-align: center;
}

	
.feature {
    width: 300px;
    height: 280px;
    background: url(../../assets/img/feature-bg1.png) no-repeat center;
    color: white;
    font-size: 26px;
    text-align: center;
    padding-top: 70px;
    line-height: 30px;
    position: absolute;
    top: 0px;
    left: 0px;
    background-size: 100%;
}
		
.about-buttons a.btn {
    width: 100%;
    margin: 0px!important;
    margin-bottom:5px!important;
}	
	
.about-info {text-align:center;}
	
.security2 {text-align:center;}	
.security2 figure {
	    width: 82px;
    margin: 0px auto;
}	

.affiliate_image {
    position: relative;
    padding: 0px 0px;
}
	
.feature1 {
    width: 280px;
    height: 280px;
    background: url(../../assets/img/feature-bg2.png) no-repeat center;
    top: 150px;
    left: 79px;
    padding-top: 70px;
    background-size: 100%;
}

.feature2 {
    width: 322px;
    height: 378px;
    background: url(../../assets/img/feature-bg2.png) no-repeat center;
    top: 257px;
    left: 0px;
    padding-top: 120px;
    background-size: 100%;

}


	.min h2 span {
    font-size: 14px;
    }
    
    .plan-term p {
    font-size: 14px;
    font-weight: 400;
    }

	.bannerleft h1 {
		font-size: 34px;
		line-height: 32px;
	}
	
	.processors img {max-width:100%;}
	
	
	.bannerright {
    flex-direction: column;
    text-align: center;
    margin-top: 10px;
	}
	.calwrap {
		border-radius: 15px 15px 0 0;
		width: 100%;
		margin: 30px 0 0 0;
	}
	.calvaluewrap {
		width: 100%;
		background: #001d68bf;
		border-radius: 0 0 15px 15px;
	}
	.benwrap {
		flex-direction: column;
		margin: 15px 0;
	}
	.bentext {
		width: 100%;
		text-align: center;
		padding: 0 0 20px;
	}
	.benthumb {
		width: 100%;
	}
	.benwrap.odd {
		flex-direction: column;
	}
	.benwrap.odd .bentext {
		text-align: center;
	}
	.affileft {
		text-align: center;
	}
	.affiplan {
		position: relative;
	}
	.affiplan1,.affiplan2,.affiplan3,.affiplan4 {
		left: auto;
		top: auto;
		width: 100%;
		bottom: auto;
		right: auto;
	}
	.affiright {
		padding: 40px 0 0;
	}
	.benefitswrap .heading h2, .useractivitywrap .heading h2 {
		font-size: 40px;
	}
	.flogo {
		width: 100%;
		text-align: center;
		padding: 0 0 15px;
	}
	.faddrs {
		display: flex;
		justify-content: center;
		margin: 0 0 15px;
	}
	.flinkswrap {
		padding: 30px 0 0;
		text-align: center;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}
	.flinkswrap ul {
		margin: 0 0 15px;
	}
	.copyright p,.copyright h4,.social {
		text-align: center;
	}
	.statwrap {
		margin: 15px 0;
	}
	.welcomewrap {
		padding: 0 0 35px;
	}
}
@media (max-width:415px) {




.calculator-left h1 {
    text-transform: uppercase;
    font-weight: 400;
    font-size: 26px;
    line-height: 26px;
}



.calculator-left {    padding: 20px;}

	.lastwrap .infos ul li p {
		display: block;
		background: #ffed21;
		padding: 5px;
		margin: 0 0 10px;
		color: #03298b;
		font-weight: 600;
		text-transform: uppercase;
	}
	.benefitswrap .heading {
		margin: 0;
	}
	.benefitswrap .heading h2, .useractivitywrap .heading h2 {
		font-size: 30px;
		margin: 0 0 20px;
	}
	.benefitswrap .heading p, .useractivitywrap .heading p {
		font-size: 16px;
		line-height: 26px;
	}
	.benefitswrap .heading p br, .useractivitywrap .heading p br {
		display: none;
	}
	.benefitswrap .heading, {
		margin: 0 0 20px;
	}
	.affiright h2, .affiright h3 {
		font-size: 24px;
		line-height: 32px;
	}
	.nav {
		flex-direction: column;
	}
	.nav-tabs > li {

		width: 100%;
	}
	.nav-tabs > li.active {

	}
	.lastwrap .head {
		display: none;
	}
	.lastwrap .infos ul {
		flex-direction: column;
	}
	.lastwrap .infos ul li {
		width: 100% !important;
		padding: 10px;
		text-align: center;
	}
	.faddrs {
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}
	.helpcenter,.support {
		margin: 5px 0;
		width: 100%;
	}
	.affiplan h3 {
		font-size: 28px;
	}
	.affiplan h3 span {
		font-size: 22px;
	}
	.affiplan p {
		font-size: 16px;
	}
	.header .button {
		margin: 10px 0 0 0;
		text-align: right;
		width: 100%;
	}
	.welsomemsg p {
		font-size: 15px;
		line-height: 25px;
	}
}
@media (max-width:321px) {
	.bannerleft h1 {
		font-size: 30px;
	}
	.calvalbox .infos h4 {
		font-size: 30px;
		line-height: 30px;
	}
	.planbox .investnow {
		position: relative;
		float: left;
		margin: 20px 0 0 0;
		text-align: center;
		width: 100%;
	}
	.planbox .investnow a {
		border-radius: 0 0 15px 15px;
		width: 100%;
	}
	.welsomemsg h1 {
		font-size: 24px;
	}
}

/* ========================= New Sections Styling ========================= */

/* Blockchain Evolution Section */
.blockchain-evolution {
    background: rgba(0,0,0,0.3);
}

.evolution-timeline .table {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    overflow: hidden;
}

.evolution-timeline .table th {
    background: rgba(254,202,87,0.2);
    border: none;
    color: #feca57;
    font-weight: 600;
}

.evolution-timeline .table td {
    border: none;
    padding: 15px;
    vertical-align: middle;
}

/* Base of Blockchain Section */
.base-blockchain .feature-item {
    transition: transform 0.3s ease;
}

.base-blockchain .feature-item:hover {
    transform: translateY(-5px);
}

/* Start Journey Section */
.start-journey .wallet-item {
    transition: all 0.3s ease;
    cursor: pointer;
}

.start-journey .wallet-item:hover {
    transform: translateX(10px);
    background: rgba(255,255,255,0.2) !important;
}

.start-card {
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
    transition: transform 0.3s ease;
}

.start-card:hover {
    transform: translateY(-10px);
}

/* Affiliate Program Section */
.bonus-card {
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    position: relative;
    overflow: hidden;
}

.bonus-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.4);
}

.bonus-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s;
}

.bonus-card:hover:before {
    left: 100%;
}

/* Bonus Structure Section */
.bonus-detail-card {
    border: 1px solid rgba(254,202,87,0.3);
    transition: all 0.3s ease;
}

.bonus-detail-card:hover {
    border-color: #feca57;
    box-shadow: 0 10px 30px rgba(254,202,87,0.2);
}

/* Rank System Section */
.rank-card {
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.rank-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 40px rgba(0,0,0,0.4);
}

/* Terms & Conditions Section */
.terms-card {
    border-left: 4px solid #feca57;
    transition: all 0.3s ease;
}

.terms-card:hover {
    border-left-color: #ff6b6b;
    transform: translateX(10px);
}

/* Life Changing Rewards */
.life-changing-rewards {
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

.reward-item {
    transition: transform 0.3s ease;
}

.reward-item:hover {
    transform: scale(1.05);
}

/* Join Now Section */
.join-now {
    position: relative;
    overflow: hidden;
}

.join-content {
    position: relative;
    z-index: 2;
}

/* Smooth Scrolling for Navigation */
html {
    scroll-behavior: smooth;
}

/* Page Title Styling */
.page-title span {
    color: #feca57;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.page-title h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 10px 0;
}

.page-title h2 b {
    color: #feca57;
}

/* Enhanced Text Visibility */
.bonus-card h4, .rank-card h4 {
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    font-weight: 700 !important;
}

.bonus-card h3, .rank-card h3 {
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    font-weight: 800 !important;
}

.bonus-card p, .rank-card p {
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    font-weight: 500 !important;
}

/* Life Changing Rewards Special Styling */
.bonus-card:nth-child(9) {
    position: relative;
    animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
    0% {
        box-shadow: 0 10px 30px rgba(0,0,0,0.3), 0 0 0 0 rgba(241, 196, 15, 0.7);
    }
    50% {
        box-shadow: 0 20px 40px rgba(0,0,0,0.4), 0 0 0 10px rgba(241, 196, 15, 0);
    }
    100% {
        box-shadow: 0 10px 30px rgba(0,0,0,0.3), 0 0 0 0 rgba(241, 196, 15, 0);
    }
}

/* Crown Diamond Special Animation */
.rank-card:last-child {
    animation: crown-glow 3s infinite;
}

@keyframes crown-glow {
    0%, 100% {
        box-shadow: 0 10px 30px rgba(0,0,0,0.3), 0 0 0 0 rgba(241, 196, 15, 0.5);
    }
    50% {
        box-shadow: 0 20px 40px rgba(0,0,0,0.4), 0 0 0 15px rgba(241, 196, 15, 0);
    }
}

/* Better contrast for tables */
.evolution-timeline .table td strong {
    color: #f39c12;
    font-weight: 700;
}

.bonus-detail-card .table td {
    color: #ecf0f1 !important;
}

.bonus-detail-card .table th {
    color: #f39c12 !important;
    font-weight: 700;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title h2 {
        font-size: 2rem;
    }

    .bonus-card, .rank-card {
        margin-bottom: 20px;
    }

    .start-card {
        margin-top: 30px;
    }

    .evolution-timeline .table {
        font-size: 14px;
    }

    .bonus-card h4, .rank-card h4 {
        font-size: 1.1rem;
    }

    .bonus-card h3, .rank-card h3 {
        font-size: 1.3rem;
    }
}
