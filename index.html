<!DOCTYPE html>
<html class="no-js" lang="zxx">
<head>
<meta charset="utf-8" />
<meta http-equiv="x-ua-compatible" content="ie=edge" />
<meta name="google-site-verification" content="c3nHakgUXNlzKDAvc80qcCUWCCU09RUYjXZ-FZZCgRo" />
<title>KnowBiz - Smart Arbitrage Trading Bot | Automated Crypto Trading Platform</title>
<meta name="description" content="Join KnowBiz - The revolutionary arbitrage trading bot platform. Start with just $100 USDT and unlock consistent passive income through our automated trading system and comprehensive affiliate program.">
<meta name="keywords" content="KnowBiz, Arbitrage Trading Bot, Crypto Trading, Automated Trading, USDT BEP20, Passive Income, Trading Packages, Referral Income, Level ROI, Team Rewards, Royalty Income, Cryptocurrency, Bitcoin, Ethereum, Trading Bot, Financial Freedom">
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<link rel="shortcut icon" type="image/x-icon" href="assets/img/logo.png" />
	<!-- Place favicon.ico in the root directory -->

	<!-- Web Font -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700;800&display=swap" rel="stylesheet">

<!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.1/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-+0n0xVW2eSR5OomGNYDnhzAbDsOXxcvSN1TPprVMTNDbiYZCxYbOOl7+AMvyTG2x" crossorigin="anonymous">
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.3/font/bootstrap-icons.css">
	<link rel="stylesheet" href="assets/css/LineIcons.2.0.css" />
	<link rel="stylesheet" href="assets/css/animate.css" />
	<link rel="stylesheet" href="assets/css/custom.css?v4" />
	<link rel="stylesheet" href="assets/css/stylesheet.css" />

	<!-- Custom Responsive Styles -->
	<style>
		/* Mobile First Responsive Design */
		@media (max-width: 768px) {
			.plan-box {
				margin-bottom: 20px;
			}

			.trading-info {
				text-align: center;
			}

			.trading-info .row .col-md-4 {
				margin-bottom: 15px;
			}

			.table-responsive {
				font-size: 14px;
			}

			.page-title h2 {
				font-size: 1.8rem;
			}

			.banner-middle h2 {
				font-size: 1.5rem;
			}

			.join-card {
				margin-top: 30px;
			}

			.benefit-item {
				font-size: 14px;
			}

			.terms-card {
				margin-bottom: 20px;
			}

			.vision-card {
				margin-bottom: 20px;
			}

			.crypto-info-content, .crypto-trading-info {
				margin-bottom: 30px;
			}

			.bot-features .feature-item {
				margin-bottom: 20px;
			}

			.level-roi-info .table-responsive {
				margin-top: 20px;
			}

			.referral-tiers .tier-item {
				margin-bottom: 15px;
			}
		}

		@media (max-width: 576px) {
			.plan-box h3 {
				font-size: 1.3rem;
			}

			.page-title span {
				font-size: 0.9rem;
			}

			.navbar-nav .nav-link {
				padding: 8px 12px;
				font-size: 14px;
			}

			.btn-lg {
				padding: 12px 25px;
				font-size: 16px;
			}

			.feature-item .feature-content h4 {
				font-size: 1.1rem;
			}

			.stat-box h2 {
				font-size: 1.5rem;
			}
		}

		/* Ensure tables are always responsive */
		.table-responsive {
			overflow-x: auto;
			-webkit-overflow-scrolling: touch;
		}

		/* Make cards equal height on all screen sizes */
		.h-100 {
			height: 100% !important;
		}

		/* Improve button spacing on mobile */
		@media (max-width: 768px) {
			.banners-bottom .btn {
				display: block;
				width: 100%;
				margin-bottom: 10px;
			}

			.cta-buttons .btn {
				display: block;
				width: 100%;
				margin-bottom: 15px;
			}
		}

		/* Enhanced Color Scheme and UI Improvements */
		.plan-box {
			transition: all 0.3s ease;
			border: 2px solid transparent;
			box-shadow: 0 5px 15px rgba(0,0,0,0.1);
		}

		.plan-box:hover {
			transform: translateY(-5px);
			box-shadow: 0 10px 25px rgba(0,0,0,0.2);
			border-color: #feca57;
		}

		.stat-box {
			transition: all 0.3s ease;
			border: 1px solid rgba(255,255,255,0.1);
		}

		.stat-box:hover {
			transform: translateY(-3px);
			box-shadow: 0 8px 20px rgba(0,0,0,0.15);
		}

		.terms-card {
			transition: all 0.3s ease;
			border: 1px solid rgba(255,255,255,0.1);
		}

		.terms-card:hover {
			transform: translateY(-2px);
			box-shadow: 0 6px 18px rgba(0,0,0,0.12);
			border-color: rgba(254, 202, 87, 0.3);
		}

		.vision-card {
			transition: all 0.3s ease;
		}

		.vision-card:hover {
			transform: translateY(-2px);
			box-shadow: 0 8px 20px rgba(0,0,0,0.15);
		}

		/* Improved gradient colors */
		.gradient-gold {
			background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
		}

		.gradient-blue {
			background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
		}

		.gradient-green {
			background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
		}

		.gradient-purple {
			background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
		}

		/* Enhanced button styles */
		.btn-primary {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			border: none;
			transition: all 0.3s ease;
		}

		.btn-primary:hover {
			background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
			transform: translateY(-2px);
			box-shadow: 0 5px 15px rgba(0,0,0,0.2);
		}

		.btn-warning {
			background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
			border: none;
			color: #2c3e50 !important;
			font-weight: 600;
			transition: all 0.3s ease;
		}

		.btn-warning:hover {
			background: linear-gradient(135deg, #ff9ff3 0%, #feca57 100%);
			transform: translateY(-2px);
			box-shadow: 0 5px 15px rgba(0,0,0,0.2);
			color: #2c3e50 !important;
		}

		/* Table improvements */
		.table-dark {
			background: rgba(0,0,0,0.3);
			backdrop-filter: blur(10px);
		}

		.table-dark th {
			background: rgba(254, 202, 87, 0.2);
			color: #feca57;
			font-weight: 600;
		}

		.table-dark td {
			border-color: rgba(255,255,255,0.1);
		}

		/* Icon improvements */
		.feature-icon, .benefit-icon, .tier-icon {
			transition: all 0.3s ease;
		}

		.feature-icon:hover, .benefit-icon:hover, .tier-icon:hover {
			transform: scale(1.1);
		}

		/* Text color improvements */
		.text-gold {
			color: #f39c12 !important;
		}

		.text-blue {
			color: #3498db !important;
		}

		.text-purple {
			color: #8e44ad !important;
		}

		/* Additional button styles */
		.btn-success {
			background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
			border: none;
			transition: all 0.3s ease;
		}

		.btn-success:hover {
			background: linear-gradient(135deg, #229954 0%, #27ae60 100%);
			transform: translateY(-2px);
			box-shadow: 0 5px 15px rgba(0,0,0,0.2);
		}

		.btn-info {
			background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
			border: none;
			transition: all 0.3s ease;
		}

		.btn-info:hover {
			background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
			transform: translateY(-2px);
			box-shadow: 0 5px 15px rgba(0,0,0,0.2);
		}

		.btn-secondary {
			background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
			border: none;
			transition: all 0.3s ease;
		}

		.btn-secondary:hover {
			background: linear-gradient(135deg, #7f8c8d 0%, #95a5a6 100%);
			transform: translateY(-2px);
			box-shadow: 0 5px 15px rgba(0,0,0,0.2);
		}

		/* Page title enhancements */
		.page-title span {
			background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			background-clip: text;
			font-weight: 600;
		}

		/* Smooth scrolling */
		html {
			scroll-behavior: smooth;
		}

		/* Loading animation for wow elements */
		.wow {
			visibility: hidden;
		}

		/* Enhanced shadows and depth */
		.container {
			position: relative;
		}

		/* Improved spacing */
		section {
			padding: 80px 0;
		}

		@media (max-width: 768px) {
			section {
				padding: 60px 0;
			}
		}

		/* Final polish and animations */
		.trading-info {
			background: rgba(255,255,255,0.05);
			backdrop-filter: blur(10px);
			border-radius: 15px;
			border: 1px solid rgba(255,255,255,0.1);
		}

		.resultbox {
			transition: all 0.3s ease;
			border-radius: 10px;
			border: 1px solid rgba(255,255,255,0.1);
		}

		.resultbox:hover {
			transform: translateY(-2px);
			box-shadow: 0 5px 15px rgba(0,0,0,0.1);
		}

		/* Enhanced navbar */
		.navbar-nav .nav-link {
			transition: all 0.3s ease;
			position: relative;
		}

		.navbar-nav .nav-link:hover {
			color: #feca57 !important;
		}

		.navbar-nav .nav-link::after {
			content: '';
			position: absolute;
			width: 0;
			height: 2px;
			bottom: 0;
			left: 50%;
			background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
			transition: all 0.3s ease;
		}

		.navbar-nav .nav-link:hover::after {
			width: 100%;
			left: 0;
		}

		/* Glowing effect for VIP package */
		.plan-box[style*="8e44ad"] {
			animation: glow 2s ease-in-out infinite alternate;
		}

		@keyframes glow {
			from {
				box-shadow: 0 0 20px rgba(241, 196, 15, 0.5);
			}
			to {
				box-shadow: 0 0 30px rgba(241, 196, 15, 0.8), 0 0 40px rgba(241, 196, 15, 0.3);
			}
		}

		/* Improved mobile navigation */
		@media (max-width: 991px) {
			.navbar-collapse {
				background: rgba(0,0,0,0.9);
				backdrop-filter: blur(10px);
				border-radius: 10px;
				margin-top: 10px;
				padding: 20px;
			}
		}
	</style>
	
	<!-- ========================= JS here ========================= --> 
	<script src="http://ajax.googleapis.com/ajax/libs/jquery/1/jquery.min.js"></script>
	 <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.1/dist/js/bootstrap.bundle.min.js" integrity="sha384-gtEjrD/SeCtmISkJkNUaaKMoLD0//ElJ19smozuHV6z3Iehds+3Ulb9Bn9Plx0x4" crossorigin="anonymous"></script>
	  <script src="assets/js/main.js?v5"></script>
	 <script src="assets/js/wow.min.js"></script>
    	
    	 <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-5Z21CYLGWV"></script>
        <script>
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
        
          gtag('config', 'G-5Z21CYLGWV');
        </script>
    	 
    	 
</head>

<body>
	
	
	
	

	
	

<section class="top-part">
<div class="top-part-inner">
<div class="yellow-box"></div>
<!-- <a class="telegram-top" href="#"target="_blank"></a> -->

		
<!-- ========================= header start ========================= -->	
<header class="header navbar-area">
  <div class="container">

  
  <div class="row">
	  
	  
	  <div class="col-lg-3">
	  <a class="navbar-brand" href="index.html"><img src="assets/img/logo.png" style="height: 100px;"></a>
	  </div>
	  
	    <div class="col-lg-9">
			<div class="menu-area">
			
			<section class="prices">
					<div class="row">
						<div class="col-lg-12 px-0">
							<div class="price">
								<figure>
									<img src="assets/img/btc.png"> BTC
								</figure>
								<div class="value">
									<p class="bitCoin"></p>
								</div>
							</div>
							
							<div class="price">
								<figure>
									<img src="assets/img/eth.png"> ETH
									
								</figure>
								<div class="value">
									<p class="ethCoin"></p>
									
								</div>
							</div>
							
							<div class="price">
								<figure>
									<img src="assets/img/ltc.png"> LTC
								
								</figure>
								<div class="value">
									<p class="liteCoin"></p>
								
								</div>
							</div>
							
							<div class="price">
								<figure>
									<img src="assets/img/doge.png"> DOGE
									<i class="dogeCoin_change1"></i>
								</figure>
								<div class="value">
									<p class="dogeCoin"></p>
							
								</div>
							</div>
							
							
							
								<div class="price">
								<figure>
									<img src="assets/img/xrp.png"> XRP
								
								</figure>
								<div class="value">
									<p class="xrpCoin"></p>
									
								</div>
							</div>
							
								
							
							
						</div>
					</div>

				</section>
			
	  
		   <nav class="navbar navbar-expand-lg">
			
			<button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
			  <i class="lni lni-menu"></i>
			</button>
			<div class="collapse navbar-collapse menu-left" id="navbarSupportedContent">
			
			  <ul class="navbar-nav mx-auto mb-2 mb-lg-0">
				<li class="nav-item">
			  <a class="nav-link" href="#home">Home</a>
				</li>
				<li class="nav-item">
				  <a class="nav-link" href="#trading-packages">Trading Packages</a>
				</li>
				<li class="nav-item">
				  <a class="nav-link" href="#arbitrage-bot">Arbitrage Bot</a>
				</li>
				<li class="nav-item">
				  <a class="nav-link" href="#income-structure">Income Structure</a>
				</li>
				<li class="nav-item">
				  <a class="nav-link" href="#referral-income">Referral Income</a>
				</li>
				<li class="nav-item">
				  <a class="nav-link" href="#team-rewards">Team Rewards</a>
				</li>
				<li class="nav-item">
				  <a class="nav-link" href="#royalty-income">Royalty Income</a>
				</li>
				<li class="nav-item">
				  <a class="nav-link" href="#terms">Terms</a>
				</li>
				<li class="nav-item">
				  <a class="nav-link btn btn-warning text-white px-3" href="#join-now">Join Now</a>
				</li>



			  </ul>
			 
			</div>
		  </div>
			  
		</nav>
		
		
	<div class="days-online bounceInDown wow">
		<img src="assets/img/online.png"/> Online Since <b>2013</b>
	</div>	
		
		</div>
		</div>
	</div><!--end row-->	
  </div>
  <!-- container --> 

</header>
<!-- ========================= header end ========================= -->	
	


<section class="bannerwrap" id="home">
    <div class="container">
      <div class="row">

        <div class="col-lg-6 col-sm-12">
          <div class="bannerright">
				<div class="decentralized-badge bounceInLeft wow" style="display: inline-block; background: linear-gradient(45deg, #ff6b6b, #feca57); color: white; padding: 8px 20px; border-radius: 25px; font-weight: 600; margin-bottom: 1rem;">
					🤖 Smart Arbitrage Trading Bot
				</div>
				<h1 class="bounceInLeft wow"><span>KnowBiz</span> Automated Trading Platform</h1>

					<p class="p-big bounceInLeft wow">Revolutionary <span>Arbitrage Trading Bot</span> with Consistent Low-Risk Returns powered by AI Technology</p>
					<div class="banners-bottom bounceInLeft wow">
						<a href="#trading-packages" class="btn btn-primary">View Trading Packages</a>
						<a href="#arbitrage-bot" class="btn btn-warning">Learn About Bot</a>
					</div>

					<div class="website-badge mt-4">
						<p style="color: #feca57; font-size: 18px; font-weight: 600;">
							🌐 www.knowbiz.biz
						</p>
					</div>
					
					<div class="stats-box">
						<div class="row">
							<div class="col-lg-6">
																
								<div class="stat-block bounceInUp wow">
									<figure><img src="assets/img/stat-ic1.png"/></figure>
									<p>Started Date</p>
									<h2>Aug 25, 2013</h2>
								</div>
															</div>
							
							<div class="col-lg-6">
																
								<div class="stat-block stat-block2 bounceInUp wow">
									<figure><img src="assets/img/stat-ic2.png"/></figure>
									<p>Online Days</p>
									<h2>3595</h2>
								</div>
															</div>
							
						</div>
					</div>


          </div>
        </div>
        
        
        
          <div class="col-lg-6 col-sm-12">
			  <div id="banner-img" class="bounceInRight wow">
				<img class="bottom" src="assets/img/banner-img.png" />
				<img class="top" src="assets/img/banner-img2.png" />
			</div>
		</div>	  
        
       
      </div>
      
    </div>    
</section>



</div>        
</section>



<section class="why-box">
	
	
<div class="banner-bottom bounceInUp wow">
	<div class="container">
		<div class="row">
			<div class="col-lg-12">
				<div class="banner-middle">
					<h2>Welcome to <span>KnowBiz</span> - Smart Trading Platform! </h2>
					<p>The world's most advanced arbitrage trading bot platform. Start with just $100 USDT and earn consistent passive income through our automated trading system with multiple income streams. </p>
				</div>
			</div>
		</div>
	</div>
</div>
        
	
	<div class="container">
		<div class="row">
			<div class="col-lg-5">
				<figure class="bounceInLeft wow"><img src="assets/img/about-img.png"></figure>
				<div class="about-info bounceInUp wow">
					<div class="row">
						<div class="col-lg-6">
							<figure><img src="assets/img/about-cert.png"></figure>
						</div>
						<div class="col-lg-6">
							<h2>KnowBiz Limited</h2>
							<p>Registration number <span>*********</span></p>
							<p class="address">
								Smart Trading Solutions, Global Operations
							</p>
						</div>
					</div>
					
					<div class="row">
						<div class="col-lg-6">
							<a href="assets/img/cert-big.png" class="btn btn-primary"><img src="assets/img/info.png"> View Certificate</a>
						</div>	
						<div class="col-lg-6">
							<a href="https://find-and-update.company-information.service.gov.uk/company/14733920" class="btn btn-warning"><img src="assets/img/magnif.png">  Company Profile</a>
						</div>	
					</div>
					
					
				</div>
			</div>
			<div class="col-lg-7 bounceInRight wow">
				<div class="why-box-text">
					<h3>About us</h3>
					<h2>smart arbitrage trading from <span>AI-powered experts</span></h2>
					<p>Our trading strategies are built on advanced arbitrage techniques powered by cutting-edge AI technology. KnowBiz Limited provides investors with consistent, low-risk returns through automated trading across multiple cryptocurrency exchanges. We leverage market inefficiencies to generate profits without relying on market direction, ensuring stable income for our users through our sophisticated trading bot system.</p>
					<div class="goal mt-3">
						<h4>Our Goal</h4>
						<p>We aim to deliver consistent and low-risk returns to our investors through automated arbitrage trading. KnowBiz Limited's mission is to provide users with reliable passive income by leveraging price differences across multiple cryptocurrency exchanges. Our AI-powered trading bot operates 24/7, ensuring maximum profit potential while minimizing risk through diversified trading strategies.</p>

					</div>
					
					
					<div class="satisfaction">
						<h4>100% User Satisfaction</h4>
						<p>Join the best automated trading platform with minimal risk and consistent returns. Our arbitrage trading bot ensures stable profits through intelligent market analysis and automated execution. With multiple income streams and instant withdrawals, KnowBiz provides the ultimate trading experience for both beginners and professionals.</p>

					</div>
				
				</div>
			</div>
		</div>	
	</div>
	<section class="investment">
		<div class="container">
			
			<!-- <div class="row">
				<div class="col-lg-12">
					<div class="promo text-center mb-3">
					<img src="assets/img/VIP1200x150.gif" style="max-width:100%;">
					</div> 
						
				</div>
			</div> -->
			
			
			<div class="row">
				<div class="col-lg-12 text-center mb-4">
					<h2><span>Trading</span> Packages</h2>
					<p>Automated arbitrage trading with <span>Consistent Returns</span></p>
				</div>
			</div>

			<div class="row g-4" id="trading-packages">
				<div class="col-xl-4 col-lg-6 col-md-6 col-sm-12 mb-4">
					<div class="plan-box bounceInLeft wow h-100" style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); border: 2px solid #27ae60;">
						<p class="mb-2" style="color: #27ae60; font-weight: 600;">STARTER SPARK</p>
						<h3 style="color: #ecf0f1;">$100 - $499<span></span></h3>
						<p style="color: #bdc3c7;">Daily ROI<br/>0.5% - 1%</p>
						<div class="profit-info mb-3">
							<p class="mb-0" style="color: #95a5a6;">Entry-level package</p>
							<h4 class="text-success">For beginners</h4>
						</div>
						<a href="deposit" class="btn btn-success w-100">Start Trading</a>
					</div>
				</div>

				<div class="col-xl-4 col-lg-6 col-md-6 col-sm-12 mb-4">
					<div class="plan-box bounceInLeft wow h-100" style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); border: 2px solid #3498db;">
						<p class="mb-2" style="color: #3498db; font-weight: 600;">SILVER SURGE</p>
						<h3 style="color: #ecf0f1;">$500 - $999<span></span></h3>
						<p style="color: #bdc3c7;">Daily ROI<br/>2% - 2.5%</p>
						<div class="profit-info mb-3">
							<p class="mb-0" style="color: #95a5a6;">Early investors</p>
							<h4 class="text-info">Modest capital</h4>
						</div>
						<a href="deposit" class="btn btn-info w-100">Start Trading</a>
					</div>
				</div>

				<div class="col-xl-4 col-lg-6 col-md-6 col-sm-12 mb-4">
					<div class="plan-box bounceInRight wow h-100" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%); border: 2px solid #f1c40f;">
						<p class="mb-2" style="color: #fff; font-weight: 600;">GOLD GROWTH</p>
						<h3 style="color: #fff;">$1000 - $1999<span></span></h3>
						<p style="color: #ecf0f1;">Daily ROI<br/>2.5% - 3%</p>
						<div class="profit-info mb-3">
							<p class="mb-0" style="color: #ecf0f1;">Balanced plan</p>
							<h4 style="color: #f1c40f;">Mid-level features</h4>
						</div>
						<a href="deposit" class="btn btn-warning w-100">Start Trading</a>
					</div>
				</div>
			</div>
			
			<div class="row g-4 mt-4">
				<div class="col-xl-4 col-lg-6 col-md-6 col-sm-12 mb-4">
					<div class="plan-box bounceInLeft wow h-100" style="background: linear-gradient(135deg, #7f8c8d 0%, #95a5a6 100%); border: 2px solid #bdc3c7;">
						<p class="mb-2" style="color: #fff; font-weight: 600;">PLATINUM PULSE</p>
						<h3 style="color: #fff;">$2000 - $5999<span></span></h3>
						<p style="color: #ecf0f1;">Daily ROI<br/>3% - 3.5%</p>
						<div class="profit-info mb-3">
							<p class="mb-0" style="color: #ecf0f1;">Advanced plan</p>
							<h4 style="color: #f39c12;">Faster returns</h4>
						</div>
						<a href="deposit" class="btn btn-secondary w-100">Start Trading</a>
					</div>
				</div>

				<div class="col-xl-4 col-lg-6 col-md-6 col-sm-12 mb-4">
					<div class="plan-box bounceInLeft wow h-100" style="background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%); border: 2px solid #1abc9c;">
						<p class="mb-2" style="color: #fff; font-weight: 600;">DIAMOND DRIVE</p>
						<h3 style="color: #fff;">$6000 - $9999<span></span></h3>
						<p style="color: #ecf0f1;">Daily ROI<br/>3.5% - 4%</p>
						<div class="profit-info mb-3">
							<p class="mb-0" style="color: #ecf0f1;">High-tier plan</p>
							<h4 style="color: #f1c40f;">Serious traders</h4>
						</div>
						<a href="deposit" class="btn btn-success w-100">Start Trading</a>
					</div>
				</div>

				<div class="col-xl-4 col-lg-12 col-md-12 col-sm-12 mb-4">
					<div class="plan-box bounceInRight wow h-100" style="background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%); border: 3px solid #f1c40f;">
						<p class="mb-2" style="color: #f1c40f;">LEGACY LEAGUE</p>
						<h3 style="color: #f1c40f;">$10000+<span></span></h3>
						<p style="color: #ecf0f1;">Daily ROI<br/>4% - 5%</p>
						<div class="profit-info mb-3">
							<p class="mb-0" style="color: #f1c40f;">VIP plan</p>
							<h4 class="text-warning">Top-tier rewards</h4>
						</div>
						<a href="deposit" class="btn btn-warning w-100">Start VIP Trading</a>
					</div>
				</div>
			</div>
			
			<div class="row mt-4">
				<div class="col-lg-12 text-center">
					<div class="trading-info p-4" style="background: rgba(255,255,255,0.1); border-radius: 15px;">
						<h3 class="mb-3">Trading Package Features</h3>
						<div class="row">
							<div class="col-md-4">
								<p><strong>Investor ID Capping:</strong> 200%</p>
							</div>
							<div class="col-md-4">
								<p><strong>Working ID Capping:</strong> 500%</p>
							</div>
							<div class="col-md-4">
								<p><strong>Payment Gateway:</strong> USDT BEP20</p>
							</div>
						</div>
						<p class="mt-3"><em>Note: Reward income is not included in working ID capping</em></p>
					</div>
				</div>
			</div>
			
			<!-- <div class="row">
				<div class="col-lg-12">
					<h2>Earnings <b>calculator</b></h2>
					<div class="calculator-block">
						<div class="row">
							<div class="col-lg-3">
										 <label>Amount</label>
									<div class="resultbox resultbox-amount">
									
											<input type="text" id="money" value="100">
										</div>
							</div>
							
							<div class="col-lg-3">
									<label>Select Plan</label>
								<div class="resultbox">
							
										<div class="plan-drop">
										
										  <select id="Ultra">
											<option value="0">up to 5.55% for 65 b.days</option>
											<option value="1">555% after 25 b.days</option>
											<option value="2">655% after 35 b.days</option>
											<option value="3">1555% after 75 days</option>
											

										  </select>
										  
										
										</div>
									</div>
								
								
							</div>
							
							
							<div class="col-lg-3">
									
								
										
										  <label>Daily Profit</label>
										 <div class="resultbox"> 
										  <span id="profitDaily">$0.30</span>
									
									  </div>
								
							</div>	
							
							
							<div class="col-lg-3">
									  <label>Total Return</label>
										<div class="resultbox">

										  <span id="profitTotal">$0.30</span>
									
									  </div>
								
								
								
							</div>	
							
							
							
						</div>
					</div>
				</div>
			</div> -->
			
		</div>		
	</section>
	
</section>







<section class="features_block">

	<div class="container">
		<div class="page-title mb-3">
			<span>platform features</span>
			why choose <b>KnowBiz</b> arbitrage trading bot?
		</div>
		
		
		<div class="row">
			<div class="col-lg-3 bounceInLeft wow">
				<div class="feat-block">
					<div class="feat-top">
						<figure><img src="assets/img/feat1.png"/></figure>
						<h2>24/7 Automated<br/>Trading</h2>

					</div>
			</div>
				
				
				
			</div>	
			
			<div class="col-lg-3 bounceInLeft wow">
			
				
				<div class="feat-block">
					<div class="feat-top">
						<figure><img src="assets/img/feat2.png"/></figure>
						<h2>Multi-Exchange<br/>Arbitrage</h2>
					</div>
				</div>
			</div>	
			
			
			<div class="col-lg-3 bounceInRight wow">
				
				<div class="feat-block">
					<div class="feat-top">
						<figure><img src="assets/img/feat3.png"/></figure>
						<h2>Low Risk<br/>High Returns</h2>
					</div>
				</div>
	
				
			</div>	
			
			
			<div class="col-lg-3">
				
				<div class="feat-block bounceInRight wow">
					<div class="feat-top">
						<figure><img src="assets/img/feat4.png"/></figure>
						<h2>Instant<br/>Withdrawals</h2>
					</div>
				</div>
	
				
			</div>	
			
			
		</div>
		
	</div>
</section>





<section class="statistics">
	<div class="container">
		<div class="row">
			<div class="col-lg-5">
				<figure><img src="assets/img/aff-robot.png"/></figure>
			</div>
			<div class="col-lg-7">
				
				<div class="row">
					<div class="col-lg-12"><h3><span>Income</span> Structure</h3></div>
				</div>

				<div class="row">
						<div class="col-lg-3 col-md-6 col-sm-12 mb-3">
								<div class="stat-box" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); border: 2px solid #e74c3c;">
										<h2 style="color: #fff;">5-10<span>%</span></h2>
										<p style="color: #ecf0f1;">Direct Referral Income</p>
								</div>
						</div>
						<div class="col-lg-3 col-md-6 col-sm-12 mb-3">
							<div class="stat-box" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); border: 2px solid #3498db;">
								<h2 style="color: #fff;">15<span>%</span></h2>
								<p style="color: #ecf0f1;">Level 1 ROI</p>
							</div>
						</div>
						<div class="col-lg-3 col-md-6 col-sm-12 mb-3">
							<div class="stat-box" style="background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%); border: 2px solid #9b59b6;">
								<h2 style="color: #fff;">9<span>%</span></h2>
								<p style="color: #ecf0f1;">Level 2 ROI</p>
							</div>
						</div>
						<div class="col-lg-3 col-md-6 col-sm-12 mb-3">
							<div class="stat-box" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%); border: 2px solid #27ae60;">
								<h2 style="color: #fff;">6<span>%</span></h2>
								<p style="color: #ecf0f1;">Level 3 ROI</p>
							</div>
						</div>
				</div>
			</div>
		</div>
		

			
	</div>
</section>



	


<section class="affiliate_area">
	<div class="container">
		
<div class="page-title mb-3">
			<span>platform activity</span>
			<b>KnowBiz</b> Trading Statistics
		</div>
		
		<div class="row">
			<div class="col-lg-3">
								
				<div class="resultbox d-flex justify-content-between">
					<p><span>$8125839.67</span>Total Trading Volume</p>

					<figure><img src="assets/img/stat-sm1.png"/></figure>
				</div>
							</div>
			<div class="col-lg-3">
								
				<div class="resultbox d-flex justify-content-between">
					<p><span>3595</span>Online Days</p>
				
				<figure><img src="assets/img/stat-sm2.png"/></figure>
				</div>
							</div>
			
			<div class="col-lg-3">
								
				<div class="resultbox d-flex justify-content-between">
					<p><span>24</span>Visitors Online</p>
				
				<figure><img src="assets/img/stat-sm3.png"/></figure>
				</div>
							</div>
			
			<div class="col-lg-3">
								
				<div class="resultbox d-flex justify-content-between">
					<p><span>$19232322.16</span>Total Profits Generated</p>

				<figure><img src="assets/img/stat-sm6.png"/></figure>
				</div>
							</div>
			
		</div>
	
	
		<div class="row">
			<div class="col-lg-3 offset-md-1">
								
				<div class="resultbox d-flex justify-content-between">
					<p><span>Aug 25, 2013</span>Started Date</p>
				
				<figure><img src="assets/img/stat-sm4.png"/></figure>
				</div>
							</div>
			
			<div class="col-lg-3">
								
				<div class="resultbox d-flex justify-content-between">
					<p><span>$153.81</span>Today's Withdraw</p>
				
					<figure><img src="assets/img/stat-sm5.png"/></figure>
				</div>
							</div>
			
			<div class="col-lg-3">
								
				<div class="resultbox d-flex justify-content-between">
					<p><span>$27.12</span>Today's Deposit</p>
					<figure><img src="assets/img/stat-sm7.png"/></figure>
				</div>
							</div>
			
			
		</div>
	
		<div class="row">
			<div class="col-lg-12">
				<div class="payment-processors">
					
					<hr>
				
					<div class="payment-block">
						<figure><img src="assets/img/payment1.png"/></figure>
						<figure><img src="assets/img/payment2.png"/></figure>
						<figure><img src="assets/img/payment3.png"/></figure>
						<figure><img src="assets/img/payment4.png"/></figure>
						<figure><img src="assets/img/payment5.png"/></figure>
						<figure><img src="assets/img/payment7.png"/></figure>
						<figure><img src="assets/img/payment8.png"/></figure>
						<figure><img src="assets/img/payment9.png"/></figure>
				</div>
				
				<div class="payment-block">		
						
						<figure><img src="assets/img/payment10.png"/></figure>
						<figure><img src="assets/img/payment11.png"/></figure>
						<figure><img src="assets/img/payment12.png"/></figure>
						<figure><img src="assets/img/payment13.png"/></figure>
						<figure><img src="assets/img/payment14.png"/></figure>
						<figure><img src="assets/img/payment15.png"/></figure>
						<figure><img src="assets/img/payment16.png"/></figure>
						
					</div>
					
						<hr>
					
				</div>
			</div>
		</div>
		
		
		
		<div class="row">
			<div class="col-lg-4">
				<div class="page-title mt-3">
					<b>Security</b> Systems
				</div>
			</div>
			<div class="col-lg-8">
				<figure><img src="assets/img/security.png"/></figure>
			</div>	
		</div>


    

		
	</div>
</section>











<script type="text/javascript">
//// Get the CryptoCurrency Information from the API
jQuery.ajax({
	url: "https://min-api.cryptocompare.com/data/pricemultifull",
	data: "fsyms=BTC,ETH,DASH,LTC,BNB,XRP,BCH,XLM,TRX,DOGE&tsyms=USD",
	dataType : 'json',
}).done(function(data) 
{
    // console.log( "BTC : " + data.RAW.BTC.USD.CHANGEPCTDAY + ", ETH : " + data.RAW.ETH.USD.CHANGEPCTDAY + ", DASH : " + data.RAW.DASH.USD.CHANGEPCTDAY + ", LTC : " + data.RAW.LTC.USD.CHANGEPCTDAY + ", XRP : " + data.RAW.XRP.USD.CHANGEPCTDAY );
    //	console.log( "BTC : " + parseFloat(data.RAW.BTC.USD.CHANGEPCTDAY).toFixed(2) + ", ETH : " + parseFloat(data.RAW.ETH.USD.CHANGEPCTDAY).toFixed(2) + ", DASH : " + parseFloat(data.RAW.DASH.USD.CHANGEPCTDAY).toFixed(2) + ", LTC : " + parseFloat(data.RAW.LTC.USD.CHANGEPCTDAY).toFixed(2) + ", XRP : " + parseFloat(data.RAW.XRP.USD.CHANGEPCTDAY).toFixed(2) );

	jQuery(".dashCoin").html('$' + parseFloat(data.RAW.DASH.USD.PRICE).toFixed(2));
	jQuery(".ethCoin").html('$' + parseFloat(data.RAW.ETH.USD.PRICE).toFixed(2));
	jQuery(".bitCoin").html('$' + parseFloat(data.RAW.BTC.USD.PRICE).toFixed(2));
	jQuery(".liteCoin").html('$' + parseFloat(data.RAW.LTC.USD.PRICE).toFixed(2));
	jQuery(".bnbCoin").html('$' + parseFloat(data.RAW.BNB.USD.PRICE).toFixed(2));
	jQuery(".xrpCoin").html('$' + parseFloat(data.RAW.XRP.USD.PRICE).toFixed(2));
	jQuery(".bchCoin").html('$' + parseFloat(data.RAW.BCH.USD.PRICE).toFixed(2));
	jQuery(".xlmCoin").html('$' + parseFloat(data.RAW.XLM.USD.PRICE).toFixed(2));
	jQuery(".dogeCoin").html('$' + parseFloat(data.RAW.DOGE.USD.PRICE).toFixed(2));
	jQuery(".trxCoin").html('$' + parseFloat(data.RAW.TRX.USD.PRICE).toFixed(2));

	var dash = parseFloat(data.RAW.DASH.USD.CHANGEPCTDAY).toFixed(2);
	var eth  = parseFloat(data.RAW.ETH.USD.CHANGEPCTDAY).toFixed(2);
	var btc = parseFloat(data.RAW.BTC.USD.CHANGEPCTDAY).toFixed(2);
	var usd  = parseFloat(data.RAW.LTC.USD.CHANGEPCTDAY).toFixed(2);
	var bnb = parseFloat(data.RAW.BNB.USD.CHANGEPCTDAY).toFixed(2);
	var xrp  = parseFloat(data.RAW.XRP.USD.CHANGEPCTDAY).toFixed(2);
	var bch  = parseFloat(data.RAW.BCH.USD.CHANGEPCTDAY).toFixed(2);
	var xlm  = parseFloat(data.RAW.XLM.USD.CHANGEPCTDAY).toFixed(2);
	var doge  = parseFloat(data.RAW.DOGE.USD.CHANGEPCTDAY).toFixed(2);
	var trx  = parseFloat(data.RAW.TRX.USD.CHANGEPCTDAY).toFixed(2);

	if( dash >= 0 ) jQuery(".dashCoin_change").addClass("greenup"); else jQuery(".dashCoin_change").addClass("purpledown");
	if( eth >= 0 ) jQuery(".ethCoin_change").addClass("greenup"); else jQuery(".ethCoin_change").addClass("purpledown");
	if( btc >= 0 ) jQuery(".bitCoin_change").addClass("greenup"); else jQuery(".bitCoin_change").addClass("purpledown");
	if( usd >= 0 ) jQuery(".liteCoin_change").addClass("greenup"); else jQuery(".liteCoin_change").addClass("purpledown");
	if( bnb >= 0 ) jQuery(".bnbCoin_change").addClass("greenup"); else jQuery(".bnbCoin_change").addClass("purpledown");
	if( xrp >= 0 ) jQuery(".xrpCoin_change").addClass("greenup"); else jQuery(".xrpCoin_change").addClass("purpledown");
	if( bch >= 0 ) jQuery(".bchCoin_change").addClass("greenup"); else jQuery(".bchCoin_change").addClass("purpledown");
	if( xlm >= 0 ) jQuery(".xlmCoin_change").addClass("greenup"); else jQuery(".xlmCoin_change").addClass("purpledown");
	if( doge >= 0 ) jQuery(".dogeCoin_change").addClass("greenup"); else jQuery(".dogeCoin_change").addClass("purpledown");
	if( trx >= 0 ) jQuery(".trxCoin_change").addClass("greenup"); else jQuery(".trxCoin_change").addClass("purpledown");
	
	
	
	if( dash >= 0 ) jQuery(".dashCoin_change1").addClass("greenup1"); else jQuery(".dashCoin_change1").addClass("purpledown1");
	if( eth >= 0 ) jQuery(".ethCoin_change1").addClass("greenup1"); else jQuery(".ethCoin_change1").addClass("purpledown1");
	if( btc >= 0 ) jQuery(".bitCoin_change1").addClass("greenup1"); else jQuery(".bitCoin_change1").addClass("purpledown1");
	if( usd >= 0 ) jQuery(".liteCoin_change1").addClass("greenup1"); else jQuery(".liteCoin_change1").addClass("purpledown1");
	if( bnb >= 0 ) jQuery(".bnbCoin_change1").addClass("greenup1"); else jQuery(".bnbCoin_change1").addClass("purpledown1");
	if( xrp >= 0 ) jQuery(".xrpCoin_change1").addClass("greenup1"); else jQuery(".xrpCoin_change1").addClass("purpledown1");
	if( bch >= 0 ) jQuery(".bchCoin_change1").addClass("greenup1"); else jQuery(".bchCoin_change1").addClass("purpledown1");
	if( xlm >= 0 ) jQuery(".xlmCoin_change1").addClass("greenup1"); else jQuery(".xlmCoin_change1").addClass("purpledown1");
	if( doge >= 0 ) jQuery(".dogeCoin_change1").addClass("greenup1"); else jQuery(".dogeCoin_change1").addClass("purpledown1");
	if( trx >= 0 ) jQuery(".trxCoin_change1").addClass("greenup1"); else jQuery(".trxCoin_change1").addClass("purpledown1");
	

	jQuery(".dashCoin_change").html( dash + "%");
	jQuery(".ethCoin_change").html( eth + "%");
	jQuery(".bitCoin_change").html( btc + "%");
	jQuery(".liteCoin_change").html( usd + "%");
	jQuery(".bnbCoin_change").html( bnb + "%");
	jQuery(".xrpCoin_change").html( xrp + "%");
	jQuery(".bchCoin_change").html( bch + "%");
	jQuery(".xlmCoin_change").html( bch + "%");
	jQuery(".dogeCoin_change").html( doge + "%");
	jQuery(".trxCoin_change").html( trx + "%");

    // VOLUME INFORMATION
    jQuery(".dashCoin_volume").html('Volume $' + data.RAW.DASH.USD.VOLUME24HOUR.toFixed(2));
	jQuery(".ethCoin_volume").html('Volume $' + data.RAW.ETH.USD.VOLUME24HOUR.toFixed(2));
	jQuery(".bitCoin_volume").html('Volume $' + data.RAW.BTC.USD.VOLUME24HOUR.toFixed(2));
	jQuery(".liteCoin_volume").html('Volume $' + data.RAW.LTC.USD.VOLUME24HOUR.toFixed(2));
	jQuery(".bnbCoin_volume").html('Volume $' + data.RAW.BNB.USD.VOLUME24HOUR.toFixed(2));
	jQuery(".xrpCoin_volume").html('Volume $' + data.RAW.XRP.USD.VOLUME24HOUR.toFixed(2));
	jQuery(".bchCoin_volume").html('Volume $' + data.RAW.BCH.USD.VOLUME24HOUR.toFixed(2));
	jQuery(".xlmCoin_volume").html('Volume $' + data.RAW.XLM.USD.VOLUME24HOUR.toFixed(2));
	jQuery(".dogeCoin_volume").html('Volume $' + data.RAW.DOGE.USD.VOLUME24HOUR.toFixed(2));
	jQuery(".trxCoin_volume").html('Volume $' + data.RAW.TRX.USD.VOLUME24HOUR.toFixed(2));

});


var x, i, j, l, ll, selElmnt, a, b, c;
/* Look for any elements with the class "custom-select": */
x = document.getElementsByClassName("custom-select");
l = x.length;
for (i = 0; i < l; i++) {
  selElmnt = x[i].getElementsByTagName("select")[0];
  ll = selElmnt.length;
  /* For each element, create a new DIV that will act as the selected item: */
  a = document.createElement("DIV");
  a.setAttribute("class", "select-selected");
  a.innerHTML = selElmnt.options[selElmnt.selectedIndex].innerHTML;
  x[i].appendChild(a);
  /* For each element, create a new DIV that will contain the option list: */
  b = document.createElement("DIV");
  b.setAttribute("class", "select-items select-hide");
  for (j = 1; j < ll; j++) {
    /* For each option in the original select element,
    create a new DIV that will act as an option item: */
    c = document.createElement("DIV");
    c.innerHTML = selElmnt.options[j].innerHTML;
    c.addEventListener("click", function(e) {
        /* When an item is clicked, update the original select box,
        and the selected item: */
        var y, i, k, s, h, sl, yl;
        s = this.parentNode.parentNode.getElementsByTagName("select")[0];
        sl = s.length;
        h = this.parentNode.previousSibling;
        for (i = 0; i < sl; i++) {
          if (s.options[i].innerHTML == this.innerHTML) {
            s.selectedIndex = i;
            h.innerHTML = this.innerHTML;
            y = this.parentNode.getElementsByClassName("same-as-selected");
            yl = y.length;
            for (k = 0; k < yl; k++) {
              y[k].removeAttribute("class");
            }
            this.setAttribute("class", "same-as-selected");
            break;
          }
        }
        h.click();
    });
    b.appendChild(c);
  }
  x[i].appendChild(b);
  a.addEventListener("click", function(e) {
    /* When the select box is clicked, close any other select boxes,
    and open/close the current select box: */
    e.stopPropagation();
    closeAllSelect(this);
    this.nextSibling.classList.toggle("select-hide");
    this.classList.toggle("select-arrow-active");
  });
}

function closeAllSelect(elmnt) {
  /* A function that will close all select boxes in the document,
  except the current select box: */
  var x, y, i, xl, yl, arrNo = [];
  x = document.getElementsByClassName("select-items");
  y = document.getElementsByClassName("select-selected");
  xl = x.length;
  yl = y.length;
  for (i = 0; i < yl; i++) {
    if (elmnt == y[i]) {
      arrNo.push(i)
    } else {
      y[i].classList.remove("select-arrow-active");
    }
  }
  for (i = 0; i < xl; i++) {
    if (arrNo.indexOf(i)) {
      x[i].classList.add("select-hide");
    }
  }
}

/* If the user clicks anywhere outside the select box,
then close all select boxes: */
document.addEventListener("click", closeAllSelect);


</script>

<!-- ========================= What is Cryptocurrency Section ========================= -->
<section class="cryptocurrency-info py-5" id="cryptocurrency-info">
	<div class="container">
		<div class="row">
			<div class="col-lg-12">
				<div class="page-title text-center mb-5">
					<span>digital currency</span>
					<h2>What is <b>Cryptocurrency</b></h2>
					<p>Understanding the foundation of digital finance</p>
				</div>
			</div>
		</div>

		<div class="row">
			<div class="col-lg-6">
				<div class="crypto-info-content">
					<h4>Digital Currency Revolution</h4>
					<p>Cryptocurrencies operate on blockchain technology, a transparent and immutable ledger maintained by a distributed network of computers. Popular examples include Bitcoin, Ethereum, and Litecoin.</p>

					<h4>Key Benefits</h4>
					<ul class="crypto-benefits">
						<li>Peer-to-peer transfers without banks or intermediaries</li>
						<li>Fast, borderless, and cost-effective transactions</li>
						<li>Used for trading, investing, and online purchases</li>
						<li>Powers decentralized applications (dApps)</li>
						<li>Stored in secure digital wallets</li>
						<li>Value driven by demand, supply, and utility</li>
					</ul>
				</div>
			</div>
			<div class="col-lg-6">
				<div class="crypto-trading-info">
					<h4>Crypto Trading</h4>
					<p>Crypto trading is the process of buying, selling, or exchanging cryptocurrencies like Bitcoin, Ethereum, and others through online platforms. Traders aim to profit from price movements by analyzing market trends, news, and charts.</p>

					<div class="trading-types mt-4">
						<div class="trading-type mb-3">
							<h5>Short-term Trading</h5>
							<p>Day trading and scalping for quick profits</p>
						</div>
						<div class="trading-type mb-3">
							<h5>Long-term Investment</h5>
							<p>Holding and investing for future growth</p>
						</div>
						<div class="trading-type mb-3">
							<h5>24/7 Global Markets</h5>
							<p>Unlike traditional stocks, crypto markets never close</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- ========================= Arbitrage Trading Bot Section ========================= -->
<section class="arbitrage-bot py-5" id="arbitrage-bot" style="background: rgba(255,255,255,0.05);">
	<div class="container">
		<div class="row">
			<div class="col-lg-12">
				<div class="page-title text-center mb-5">
					<span>automated trading</span>
					<h2>Arbitrage <b>Trading Bot</b></h2>
					<p>Advanced AI-powered bot for consistent low-risk profits</p>
				</div>
			</div>
		</div>

		<div class="row">
			<div class="col-lg-6">
				<div class="bot-features">
					<div class="feature-item mb-4 d-flex">
						<div class="feature-icon me-3">
							<i class="bi bi-robot" style="font-size: 2rem; color: #feca57;"></i>
						</div>
						<div class="feature-content">
							<h4>Automated Software Tool</h4>
							<p>Scans multiple cryptocurrency exchanges to identify price differences</p>
						</div>
					</div>

					<div class="feature-item mb-4 d-flex">
						<div class="feature-icon me-3">
							<i class="bi bi-arrow-left-right" style="font-size: 2rem; color: #feca57;"></i>
						</div>
						<div class="feature-content">
							<h4>Buy Low, Sell High</h4>
							<p>Buys low on one exchange and sells high on another</p>
						</div>
					</div>

					<div class="feature-item mb-4 d-flex">
						<div class="feature-icon me-3">
							<i class="bi bi-clock" style="font-size: 2rem; color: #feca57;"></i>
						</div>
						<div class="feature-content">
							<h4>24/7 Operation</h4>
							<p>Operates continuously, reacting faster than humans</p>
						</div>
					</div>
				</div>
			</div>

			<div class="col-lg-6">
				<div class="bot-features">
					<div class="feature-item mb-4 d-flex">
						<div class="feature-icon me-3">
							<i class="bi bi-shield-check" style="font-size: 2rem; color: #feca57;"></i>
						</div>
						<div class="feature-content">
							<h4>Low-Risk Strategy</h4>
							<p>Generates profit from price spreads without relying on market direction</p>
						</div>
					</div>

					<div class="feature-item mb-4 d-flex">
						<div class="feature-icon me-3">
							<i class="bi bi-graph-up" style="font-size: 2rem; color: #feca57;"></i>
						</div>
						<div class="feature-content">
							<h4>Consistent Returns</h4>
							<p>Executes dozens or hundreds of trades daily for steady income</p>
						</div>
					</div>

					<div class="feature-item mb-4 d-flex">
						<div class="feature-icon me-3">
							<i class="bi bi-cpu" style="font-size: 2rem; color: #feca57;"></i>
						</div>
						<div class="feature-content">
							<h4>Algorithmic Speed</h4>
							<p>Eliminates manual effort with lightning-fast execution</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- ========================= Income Structure Section ========================= -->
<section class="income-structure py-5" id="income-structure">
	<div class="container">
		<div class="row">
			<div class="col-lg-12">
				<div class="page-title text-center mb-5">
					<span>earning opportunities</span>
					<h2>Income <b>Structure</b></h2>
					<p>Multiple income streams for maximum earning potential</p>
				</div>
			</div>
		</div>

		<div class="row">
			<div class="col-lg-6 col-md-12 mb-4">
				<div class="income-info">
					<h3>Direct Referral Income</h3>
					<div class="referral-tiers mt-4">
						<div class="tier-item d-flex align-items-center mb-3 p-3" style="background: rgba(255,255,255,0.1); border-radius: 10px;">
							<div class="tier-icon me-3" style="width: 40px; height: 40px; background: #e74c3c; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
								<span style="color: white; font-weight: bold;">5%</span>
							</div>
							<span>1st to 5th Direct Referrals</span>
						</div>
						<div class="tier-item d-flex align-items-center mb-3 p-3" style="background: rgba(255,255,255,0.1); border-radius: 10px;">
							<div class="tier-icon me-3" style="width: 40px; height: 40px; background: #f39c12; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
								<span style="color: white; font-weight: bold;">7%</span>
							</div>
							<span>6th to 10th Direct Referrals</span>
						</div>
						<div class="tier-item d-flex align-items-center mb-3 p-3" style="background: rgba(255,255,255,0.1); border-radius: 10px;">
							<div class="tier-icon me-3" style="width: 40px; height: 40px; background: #27ae60; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
								<span style="color: white; font-weight: bold;">10%</span>
							</div>
							<span>11th and Above Direct Referrals</span>
						</div>
					</div>
				</div>
			</div>

			<div class="col-lg-6 col-md-12 mb-4">
				<div class="level-roi-info">
					<h3>Level ROI Income</h3>
					<div class="table-responsive mt-4">
						<table class="table table-dark table-striped">
							<thead>
								<tr>
									<th>Level</th>
									<th>ROI %</th>
									<th>Requirement</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>1st Level</td>
									<td><span class="text-success">15%</span></td>
									<td>One direct compulsory</td>
								</tr>
								<tr>
									<td>2nd Level</td>
									<td><span class="text-success">9%</span></td>
									<td>One direct compulsory</td>
								</tr>
								<tr>
									<td>3rd Level</td>
									<td><span class="text-success">6%</span></td>
									<td>One direct compulsory</td>
								</tr>
								<tr>
									<td>4th Level</td>
									<td><span class="text-success">4%</span></td>
									<td>One direct compulsory</td>
								</tr>
								<tr>
									<td>5th Level</td>
									<td><span class="text-success">2%</span></td>
									<td>One direct compulsory</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- ========================= Team Rewards Section ========================= -->
<section class="team-rewards py-5" id="team-rewards" style="background: rgba(255,255,255,0.05);">
	<div class="container">
		<div class="row">
			<div class="col-lg-12">
				<div class="page-title text-center mb-5">
					<span>team building</span>
					<h2>Active Team <b>Rewards</b></h2>
					<p>Earn bonuses by building and maintaining active teams</p>
				</div>
			</div>
		</div>

		<div class="row">
			<div class="col-lg-12">
				<div class="table-responsive">
					<table class="table table-dark table-striped">
						<thead>
							<tr>
								<th>Direct Team</th>
								<th>Total Team</th>
								<th>Bonus</th>
								<th>Remarks</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<td>-</td>
								<td>25</td>
								<td><span class="text-success">$50</span></td>
								<td>Entry level team bonus</td>
							</tr>
							<tr>
								<td>2</td>
								<td>75</td>
								<td><span class="text-success">$100</span></td>
								<td>Growing team reward</td>
							</tr>
							<tr>
								<td>3</td>
								<td>300</td>
								<td><span class="text-success">$150</span></td>
								<td>Expanding network bonus</td>
							</tr>
							<tr>
								<td>5</td>
								<td>500</td>
								<td><span class="text-success">$200</span></td>
								<td>Strong team achievement</td>
							</tr>
							<tr>
								<td>7</td>
								<td>1000</td>
								<td><span class="text-success">$300</span></td>
								<td>Leadership milestone</td>
							</tr>
							<tr>
								<td>10</td>
								<td>2000</td>
								<td><span class="text-success">$500</span></td>
								<td>Advanced team builder</td>
							</tr>
							<tr>
								<td>15</td>
								<td>5000</td>
								<td><span class="text-success">$1000</span></td>
								<td>Elite team leader</td>
							</tr>
							<tr>
								<td>20</td>
								<td>10000</td>
								<td><span class="text-success">$2000</span></td>
								<td>Master team builder</td>
							</tr>
						</tbody>
					</table>
				</div>
				<p class="text-center mt-3"><em>Note: Team count is calculated next-to-next</em></p>
			</div>
		</div>
	</div>
</section>

<!-- ========================= Royalty Income Section ========================= -->
<section class="royalty-income py-5" id="royalty-income">
	<div class="container">
		<div class="row">
			<div class="col-lg-12">
				<div class="page-title text-center mb-5">
					<span>elite rewards</span>
					<h2>Royalty <b>Income</b></h2>
					<p>Top performer rewards with company-wide revenue sharing</p>
				</div>
			</div>
		</div>

		<div class="row">
			<div class="col-lg-12">
				<div class="table-responsive">
					<table class="table table-dark table-striped">
						<thead>
							<tr>
								<th>Period</th>
								<th>Total Team</th>
								<th>Direct Team</th>
								<th>Total Business</th>
								<th>Reward</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<td>30 Days</td>
								<td>100</td>
								<td>10</td>
								<td>$20,000</td>
								<td><span class="text-success">$1,000</span></td>
							</tr>
							<tr>
								<td>30 Days</td>
								<td>200</td>
								<td>20</td>
								<td>$50,000</td>
								<td><span class="text-success">$2,000</span></td>
							</tr>
							<tr>
								<td>90 Days</td>
								<td>500</td>
								<td>50</td>
								<td>$100,000</td>
								<td><span class="text-success">$3,000</span></td>
							</tr>
							<tr>
								<td>120 Days</td>
								<td>1,000</td>
								<td>100</td>
								<td>$500,000</td>
								<td><span class="text-success">$5,000</span></td>
							</tr>
							<tr style="background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);">
								<td style="color: #f1c40f; font-weight: bold;">150 Days</td>
								<td style="color: #f1c40f; font-weight: bold;">2,000</td>
								<td style="color: #f1c40f; font-weight: bold;">200</td>
								<td style="color: #f1c40f; font-weight: bold;">$1,000,000</td>
								<td style="color: #f1c40f; font-weight: bold; font-size: 1.2rem;">$10,000</td>
							</tr>
						</tbody>
					</table>
				</div>
				<div class="royalty-info mt-4 p-4" style="background: rgba(255,255,255,0.1); border-radius: 15px;">
					<h4 class="text-warning">Royalty Income Features:</h4>
					<ul class="mt-3">
						<li>Rewards top performers with company-wide revenue sharing</li>
						<li>Recognizes leadership, consistency, and rank achievements</li>
						<li>Offers long-term passive income from overall network performance</li>
						<li>Based on team building and business volume achievements</li>
					</ul>
				</div>
			</div>
		</div>
	</div>
</section>

		<!-- Terms and Conditions -->
		<div class="row mb-5">
			<div class="col-lg-12">
				<div class="terms-card p-4" style="background: rgba(255,255,255,0.05); border-radius: 15px;">
					<h3 class="text-warning mb-4" id="terms">Terms and Conditions</h3>
					<div class="row">
						<div class="col-lg-6">
							<ul class="terms-list">
								<li>One mobile number and one email ID can register only one account</li>
								<li>Admin and service charge: 5%</li>
								<li>Registered user ID can earn all income but withdrawal only after top-up</li>
								<li>Minimum withdrawal: $10 and multiples of $10</li>
							</ul>
						</div>
						<div class="col-lg-6">
							<ul class="terms-list">
								<li>You can use all earnings for instant withdrawal and gaming</li>
								<li>Daily trading on investment amount</li>
								<li>P2P transfer with no deduction</li>
								<li>Payment gateway: USDT BEP20</li>
							</ul>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Company Vision -->
		<div class="row mt-5">
			<div class="col-lg-12">
				<div class="vision-card p-4" style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); border-radius: 15px; border: 2px solid #3498db;">
					<h3 class="text-info mb-4">
						<i class="bi bi-eye me-2"></i>Company Vision
					</h3>
					<p style="color: #ecf0f1; line-height: 1.8;">
						To empower individuals globally through smart income opportunities and cutting-edge technology. At the core of our system lies a powerful Arbitrage Trading Bot, designed to maximize trading profits by leveraging market inefficiencies across multiple exchanges. We aim to provide users with passive income, financial literacy, and long-term stability through automation and decentralization. By combining blockchain transparency, referral-based rewards, and sustainable trading strategies, we foster a strong, growth-oriented community. We are committed to ethical practices, member success, and scalable solutions that create value for everyone. Our ultimate goal is to lead a global movement where anyone can achieve financial freedom and digital independence through innovation and teamwork.
					</p>
				</div>
			</div>
		</div>

		<!-- Our Vision -->
		<div class="row mt-4">
			<div class="col-lg-12">
				<div class="vision-card p-4" style="background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%); border-radius: 15px; border: 2px solid #f1c40f;">
					<h3 style="color: #f1c40f; font-weight: bold;" class="mb-4">
						<i class="bi bi-lightbulb me-2"></i>Our Vision
					</h3>
					<p style="color: #ecf0f1; line-height: 1.8;">
						Our Vision is to empower individuals worldwide with a smart, secure, and automated Arbitrage Trading Bot that generates consistent, low-risk profits by leveraging price differences across multiple crypto exchanges. We aim to simplify complex trading strategies through intelligent automation, giving both beginners and professionals a reliable income stream. Our goal is to create a transparent and user-friendly ecosystem where users can participate in the crypto market without technical barriers. Through continuous innovation, accuracy, and performance-driven tools, we envision becoming a global leader in automated crypto trading, helping users achieve financial freedom and long-term success in the digital economy.
					</p>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- ========================= Join Now Section ========================= -->
<section class="join-now py-5" id="join-now" style="background: rgba(255,255,255,0.05);">
	<div class="container">
		<div class="row">
			<div class="col-lg-12">
				<div class="page-title text-center mb-5">
					<span>get started today</span>
					<h2>Join <b>KnowBiz</b></h2>
					<p>Start your journey to financial freedom with our automated trading bot</p>
				</div>
			</div>
		</div>

		<div class="row align-items-center">
			<div class="col-lg-6 col-md-12 mb-4">
				<div class="join-info">
					<h3>Why Choose KnowBiz?</h3>
					<div class="benefits-list mt-4">
						<div class="benefit-item d-flex align-items-center mb-3">
							<div class="benefit-icon me-3" style="width: 40px; height: 40px; background: #27ae60; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
								<i class="bi bi-check" style="color: white; font-weight: bold;"></i>
							</div>
							<span>Automated 24/7 arbitrage trading</span>
						</div>
						<div class="benefit-item d-flex align-items-center mb-3">
							<div class="benefit-icon me-3" style="width: 40px; height: 40px; background: #27ae60; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
								<i class="bi bi-check" style="color: white; font-weight: bold;"></i>
							</div>
							<span>Multiple income streams</span>
						</div>
						<div class="benefit-item d-flex align-items-center mb-3">
							<div class="benefit-icon me-3" style="width: 40px; height: 40px; background: #27ae60; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
								<i class="bi bi-check" style="color: white; font-weight: bold;"></i>
							</div>
							<span>Low-risk, consistent returns</span>
						</div>
						<div class="benefit-item d-flex align-items-center mb-3">
							<div class="benefit-icon me-3" style="width: 40px; height: 40px; background: #27ae60; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
								<i class="bi bi-check" style="color: white; font-weight: bold;"></i>
							</div>
							<span>Instant withdrawals with USDT BEP20</span>
						</div>
					</div>
				</div>
			</div>

			<div class="col-lg-6 col-md-12 mb-4">
				<div class="join-card text-center p-4 p-lg-5" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 20px;">
					<h3 class="text-white mb-4">Ready to Start Trading?</h3>
					<div class="entry-requirements mb-4">
						<h4 class="text-warning">Minimum Investment</h4>
						<h2 class="text-white">$100 USDT</h2>
						<p class="text-light">Start with Starter Spark package</p>
					</div>
					<div class="cta-buttons">
						<a href="#trading-packages" class="btn btn-warning btn-lg px-4 px-lg-5 mb-3 w-100 w-lg-auto">Choose Your Package</a>
						<br>
						<a href="https://t.me/knowbiz_support" class="btn btn-outline-light px-4 w-100 w-lg-auto" target="_blank">
							<i class="bi bi-telegram me-2"></i>Get Support
						</a>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>




<!-- ========================= Terms & Conditions Section ========================= -->
<section class="terms-conditions py-5" id="terms">
	<div class="container">
		<div class="row">
			<div class="col-lg-12">
				<div class="page-title text-center mb-5">
					<span>important information</span>
					<h2>Terms & <b>Conditions</b></h2>
					<p>Essential requirements for earning bonuses</p>
				</div>
			</div>
		</div>

		<div class="row">
			<div class="col-lg-6 col-md-12 mb-4">
				<div class="terms-card p-4" style="background: rgba(255,255,255,0.05); border-radius: 15px; height: 100%;">
					<h4 class="text-warning mb-3">1. Account Registration</h4>
					<p>One mobile number and one email ID can register only one account.</p>
				</div>
			</div>

			<div class="col-lg-6 col-md-12 mb-4">
				<div class="terms-card p-4" style="background: rgba(255,255,255,0.05); border-radius: 15px; height: 100%;">
					<h4 class="text-warning mb-3">2. Service Charges</h4>
					<p>Admin and service charge: 5% on all transactions.</p>
				</div>
			</div>

			<div class="col-lg-6 col-md-12 mb-4">
				<div class="terms-card p-4" style="background: rgba(255,255,255,0.05); border-radius: 15px; height: 100%;">
					<h4 class="text-warning mb-3">3. Withdrawal Requirements</h4>
					<p>Registered user ID can earn all income but withdrawal only after top-up. Minimum withdrawal: $10 and multiples of $10.</p>
				</div>
			</div>

			<div class="col-lg-6 col-md-12 mb-4">
				<div class="terms-card p-4" style="background: rgba(255,255,255,0.05); border-radius: 15px; height: 100%;">
					<h4 class="text-warning mb-3">4. Earnings Usage</h4>
					<p>You can use all earnings for instant withdrawal and gaming. Daily trading on investment amount.</p>
				</div>
			</div>

			<div class="col-lg-6 col-md-12 mb-4">
				<div class="terms-card p-4" style="background: rgba(255,255,255,0.05); border-radius: 15px; height: 100%;">
					<h4 class="text-warning mb-3">5. P2P Transfer</h4>
					<p>P2P transfer with no deduction between users.</p>
				</div>
			</div>

			<div class="col-lg-6 col-md-12 mb-4">
				<div class="terms-card p-4" style="background: rgba(255,255,255,0.05); border-radius: 15px; height: 100%;">
					<h4 class="text-warning mb-3">6. Payment Gateway</h4>
					<p>Payment gateway: USDT BEP20 for all transactions.</p>
				</div>
			</div>
		</div>
	</div>
</section>


<div class="row mt-3">
		
		
		<div class="col-md-12">
			
			<div class="row">
		
				<div class="col-md-4 col-sm-12">
					<div class="footer-copy">
						<img class="mb-2" src="assets/img/logo.png" style="height: 120px;"/>
						<p>© 2023. futureworld Limited. All Rights Reserved.</p>
					</div>
				</div>
				
				<div class="col-md-8 col-sm-12 footer">
				
					<div class="row">
				
							<div class="col-md-4 col-sm-12">
							<h4>Company</h4>
							<ul>
								<li><a href="about.html">About us</a></li>
								<li><a href="faq.html">Help Center</a></li>
								<li><a href="rules.html">Terms of Services</a></li>
									<li><a href="news.html">News</a></li>
							</ul>
						</div>
						
						<div class="col-md-4 col-sm-12">
							<h4>Contacts</h4>
							<ul>
								<li><a href="support.html">Support Form</a></li>
								<li><a style="text-transform:none;" href="mailto:Email">Email</a></li>
								<li><a href="#"target="_blank"><img src="assets/img/telegram.png"/> Telegram Chat</a></li>
							
							</ul>	
						</div>
						
						<div class="col-md-4 col-sm-12">
							<h4>Location</h4>
						
							<div class="footer-info">
							
								<p>20 Guild Rd, Charlton, London, England, SE7 8HN United Kingdom</p>
								
							</div>	
							
						
							
						</div>
				
						
						
					</div>
				
				</div>
				
				
				
			
			</div>
			
			
		
		</div>
		

    </div>


</body>
</html>
